"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/requests/[id]/page",{

/***/ "(app-pages-browser)/./app/admin/requests/[id]/page.tsx":
/*!******************************************!*\
  !*** ./app/admin/requests/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdRequestReviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction AdRequestReviewPage(param) {\n    let { params } = param;\n    var _placement, _placement1, _placement2, _placement3, _campaign_targeting, _campaign_targeting_countries_include, _campaign_targeting_countries_exclude, _campaign_targeting1, _campaign_targeting_pageTypes_types, _campaign_targeting2, _campaign_targeting3, _campaign_targeting4, _campaign_targeting5;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_2__.use)(params);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [placements, setPlacements] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [advertiser, setAdvertiser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [manager, setManager] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [selectedReason, setSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [rejectionReasons, setRejectionReasons] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overlappingCampaigns, setOverlappingCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AdRequestReviewPage.useEffect\": ()=>{\n            const fetchCampaignDetails = {\n                \"AdRequestReviewPage.useEffect.fetchCampaignDetails\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch campaign details from API\n                        const response = await fetch(\"/api/user/campaigns/\".concat(id));\n                        const result = await response.json();\n                        if (!result.success || !result.data) {\n                            toast({\n                                title: \"Campaign not found\",\n                                description: \"The requested campaign could not be found.\",\n                                variant: \"destructive\"\n                            });\n                            router.push(\"/admin/requests\");\n                            return;\n                        }\n                        const campaignData = result.data;\n                        setCampaign(campaignData);\n                        // For now, we'll skip overlapping campaigns check since it requires complex logic\n                        // This can be implemented later with a dedicated admin endpoint\n                        setOverlappingCampaigns([]);\n                        // Fetch ads for this campaign\n                        try {\n                            const adsResponse = await fetch(\"/api/user/campaigns/\".concat(id, \"/ads\"));\n                            const adsResult = await adsResponse.json();\n                            if (adsResult.success && adsResult.data) {\n                                setAds(adsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch ads data:\", error);\n                        }\n                        // Fetch all placements to map slot IDs to placement details\n                        try {\n                            const placementsResponse = await fetch(\"/api/placements\");\n                            const placementsResult = await placementsResponse.json();\n                            if (placementsResult.success && placementsResult.data) {\n                                setPlacements(placementsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch placements data:\", error);\n                        }\n                        // Set advertiser info from campaign data (this is the company)\n                        if (campaignData.advertiser_name) {\n                            setAdvertiser({\n                                id: campaignData.advertiser_id,\n                                name: campaignData.advertiser_name,\n                                email: campaignData.advertiser_email || \"\",\n                                role: \"company\"\n                            });\n                        }\n                        // Fetch rejection reasons\n                        try {\n                            const reasonsResponse = await fetch(\"/api/admin/rejection-reasons?entity_type=campaign\");\n                            const reasonsResult = await reasonsResponse.json();\n                            if (reasonsResult.success) {\n                                setRejectionReasons(reasonsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch rejection reasons:\", error);\n                        }\n                        // Set manager info from campaign data (this is the user managing the campaign)\n                        if (campaignData.manager_name) {\n                            setManager({\n                                id: campaignData.manager_id,\n                                name: campaignData.manager_name,\n                                email: campaignData.manager_email || \"\",\n                                role: \"user\"\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching campaign details:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load campaign details. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdRequestReviewPage.useEffect.fetchCampaignDetails\"];\n            fetchCampaignDetails();\n        }\n    }[\"AdRequestReviewPage.useEffect\"], [\n        id,\n        router,\n        toast\n    ]);\n    const handleApprove = async ()=>{\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/approve\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: feedback || \"Campaign approved\"\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to approve campaign\");\n            }\n            toast({\n                title: \"Campaign approved\",\n                description: \"The ad campaign has been approved and is now active.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error approving campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to approve the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleReject = async ()=>{\n        if (!selectedReason) {\n            toast({\n                title: \"Rejection reason required\",\n                description: \"Please select a rejection reason before rejecting the campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/reject\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    reason: selectedReason,\n                    notes: feedback.trim() || null\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to reject campaign\");\n            }\n            toast({\n                title: \"Campaign rejected\",\n                description: \"The ad campaign has been rejected with feedback.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error rejecting campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to reject the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex h-[70vh] items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg\",\n                        children: \"Loading campaign details...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 4\n        }, this);\n    }\n    if (!campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"The requested campaign could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 7\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mb-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 6\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: campaign.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.type) || \"Unknown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: campaign.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 4\n            }, this),\n            overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                variant: \"destructive\",\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertTitle, {\n                        children: \"Scheduling Conflict Detected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: [\n                            \"This campaign overlaps with \",\n                            overlappingCampaigns.length,\n                            \" existing approved campaign\",\n                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                            \" on the same placement.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                            defaultValue: \"overview\",\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"overview\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"creative\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"targeting\",\n                                            children: \"Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 8\n                                        }, this),\n                                        overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"conflicts\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Conflicts\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-white\",\n                                                    children: overlappingCampaigns.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"overview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Campaign Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign details and settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-4 md:grid-cols-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Campaign Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Name:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 309,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"URL:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.url\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 315,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Duration:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: [\n                                                                                        formatDate(campaign.startDate),\n                                                                                        \" -\",\n                                                                                        \" \",\n                                                                                        formatDate(campaign.endDate)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Submitted:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: formatDate(campaign.createdAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 328,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Placement Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: placement ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Name:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 341,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 342,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Type:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 348,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Dimensions:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 353,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.dimensions\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Price:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.priceDisplay\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 364,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Placement information not available\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"creative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Ad Creative\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the advertisement creative and content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-hidden rounded-lg border\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"aspect-video w-full overflow-hidden bg-muted\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: campaign.imageUrl || \"/placeholder.svg\",\n                                                                        alt: campaign.name,\n                                                                        className: \"h-full w-full object-contain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium\",\n                                                                            children: campaign.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: campaign.url\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Creative Specifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Format:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 407,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: ((_placement1 = placement) === null || _placement1 === void 0 ? void 0 : _placement1.format) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 408,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Dimensions:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 413,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: ((_placement2 = placement) === null || _placement2 === void 0 ? void 0 : _placement2.dimensions) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 414,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Max File Size:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: ((_placement3 = placement) === null || _placement3 === void 0 ? void 0 : _placement3.maxFileSize) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 422,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"targeting\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Targeting Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign targeting configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Geographic Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting = campaign.targeting) === null || _campaign_targeting === void 0 ? void 0 : _campaign_targeting.countries) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Mode: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 446,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"capitalize\",\n                                                                                    children: campaign.targeting.countries.mode\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Included Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 454,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_include = campaign.targeting.countries.include) === null || _campaign_targeting_countries_include === void 0 ? void 0 : _campaign_targeting_countries_include.length) > 0 ? campaign.targeting.countries.include.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 461,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Excluded Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_exclude = campaign.targeting.countries.exclude) === null || _campaign_targeting_countries_exclude === void 0 ? void 0 : _campaign_targeting_countries_exclude.length) > 0 ? campaign.targeting.countries.exclude.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 484,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 490,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 480,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Targeting all countries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No geographic targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Page Type Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting1 = campaign.targeting) === null || _campaign_targeting1 === void 0 ? void 0 : _campaign_targeting1.pageTypes) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Selected Page Types:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_pageTypes_types = campaign.targeting.pageTypes.types) === null || _campaign_targeting_pageTypes_types === void 0 ? void 0 : _campaign_targeting_pageTypes_types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: type\n                                                                                        }, type, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 523,\n                                                                                            columnNumber: 17\n                                                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No page types specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 527,\n                                                                                        columnNumber: 17\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 521,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.pageTypes.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Category Targeting:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 536,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 space-y-2\",\n                                                                                    children: Object.entries(campaign.targeting.pageTypes.categories).map((param)=>{\n                                                                                        let [pageType, categories] = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"rounded border p-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm font-medium capitalize\",\n                                                                                                    children: [\n                                                                                                        pageType,\n                                                                                                        \":\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 544,\n                                                                                                    columnNumber: 19\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                                    children: categories === \"all\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                        variant: \"outline\",\n                                                                                                        children: \"All Categories\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 549,\n                                                                                                        columnNumber: 21\n                                                                                                    }, this) : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            children: category\n                                                                                                        }, category, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                            lineNumber: 555,\n                                                                                                            columnNumber: 23\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 547,\n                                                                                                    columnNumber: 19\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, pageType, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 543,\n                                                                                            columnNumber: 18\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No page type targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Device Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting2 = campaign.targeting) === null || _campaign_targeting2 === void 0 ? void 0 : _campaign_targeting2.devices) && campaign.targeting.devices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: device\n                                                                        }, device, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No device targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Language Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting3 = campaign.targeting) === null || _campaign_targeting3 === void 0 ? void 0 : _campaign_targeting3.languages) && campaign.targeting.languages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: language.toUpperCase()\n                                                                        }, language, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No language targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 611,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Interest Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting4 = campaign.targeting) === null || _campaign_targeting4 === void 0 ? void 0 : _campaign_targeting4.interests) && campaign.targeting.interests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"capitalize\",\n                                                                            children: interest\n                                                                        }, interest, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 625,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No interest targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 635,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Age Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting5 = campaign.targeting) === null || _campaign_targeting5 === void 0 ? void 0 : _campaign_targeting5.age) && campaign.targeting.age.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.age.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: ageRange\n                                                                        }, ageRange, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No age targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 644,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"conflicts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Scheduling Conflicts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: [\n                                                            \"This campaign overlaps with \",\n                                                            overlappingCampaigns.length,\n                                                            \" existing approved campaign\",\n                                                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                            \" on the same placement\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        overlappingCampaigns.map((overlapCampaign)=>{\n                                                            var _placement;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-2 flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium\",\n                                                                                children: overlapCampaign.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 680,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"capitalize\",\n                                                                                children: overlapCampaign.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 681,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-3 flex items-center gap-2 text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    formatDate(overlapCampaign.startDate),\n                                                                                    \" -\",\n                                                                                    \" \",\n                                                                                    formatDate(overlapCampaign.endDate)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 685,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Placement\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 694,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 695,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 693,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Advertiser\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 698,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: (advertiser === null || advertiser === void 0 ? void 0 : advertiser.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 699,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 697,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, overlapCampaign.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 12\n                                                            }, this);\n                                                        }),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Conflict Resolution Options\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"ml-5 list-disc space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Adjust the campaign dates to avoid overlap\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Reject this campaign with feedback about the conflict\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Approve anyway (multiple ads will rotate in the same placement)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Cancel one of the existing campaigns to make room\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Advertiser Information (Company)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 723,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: advertiser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary\",\n                                                            children: advertiser.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: advertiser.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: advertiser.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"Company Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: advertiser.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 745,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: advertiser.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 749,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 747,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Advertiser information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Campaign Manager (User)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: manager ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-600\",\n                                                            children: manager.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: manager.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: manager.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"User Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 781,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: manager.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 780,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Role:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 785,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: manager.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 786,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Manager information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Campaign Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Review the campaign timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Campaign Duration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Start Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.startDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 811,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"End Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 814,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.endDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Duration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 818,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(campaign.endDate).getTime() - new Date(campaign.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        \"days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 819,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 804,\n                                                    columnNumber: 9\n                                                }, this),\n                                                overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-red-600 dark:text-red-400\",\n                                                                    children: \"Scheduling Conflict\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 dark:text-red-400\",\n                                                            children: [\n                                                                \"This campaign overlaps with \",\n                                                                overlappingCampaigns.length,\n                                                                \" existing approved campaign\",\n                                                                overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                                \" on the same placement.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 838,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Review Decision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Approve or reject this ad campaign request\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 850,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"rejection-reason\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Rejection Reason\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: selectedReason,\n                                                            onValueChange: setSelectedReason,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select a rejection reason...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: rejectionReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: reason.code,\n                                                                            children: reason.description\n                                                                        }, reason.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 866,\n                                                                            columnNumber: 13\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 860,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Required for rejection.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"feedback\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Additional Notes (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 875,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                            id: \"feedback\",\n                                                            placeholder: \"Provide additional feedback to the advertiser...\",\n                                                            value: feedback,\n                                                            onChange: (e)=>setFeedback(e.target.value),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Optional additional notes for the advertiser.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleReject,\n                                                disabled: submitting || !selectedReason,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Rejecting...\" : \"Reject\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"default\",\n                                                onClick: handleApprove,\n                                                disabled: submitting,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Approving...\" : \"Approve\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n        lineNumber: 248,\n        columnNumber: 3\n    }, this);\n}\n_s(AdRequestReviewPage, \"X3Wx5K5Ya7Lmjbiv094qHvsF32A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = AdRequestReviewPage;\nvar _c;\n$RefreshReg$(_c, \"AdRequestReviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/requests/[id]/page.tsx\n"));

/***/ })

});