"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/campaigns/create/page",{

/***/ "(app-pages-browser)/./components/campaigns/campaign-wizard-steps.tsx":
/*!********************************************************!*\
  !*** ./components/campaigns/campaign-wizard-steps.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdCreativeStep: () => (/* binding */ AdCreativeStep),\n/* harmony export */   PlacementStep: () => (/* binding */ PlacementStep),\n/* harmony export */   ReviewStep: () => (/* binding */ ReviewStep),\n/* harmony export */   TargetingStep: () => (/* binding */ TargetingStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swr */ \"(app-pages-browser)/./node_modules/swr/dist/index/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdCreativeStep(param) {\n    let { formData, updateFormData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        htmlFor: \"ad-title\",\n                        children: \"Ad Title *\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        id: \"ad-title\",\n                        value: formData.ad_title,\n                        onChange: (e)=>updateFormData({\n                                ad_title: e.target.value\n                            }),\n                        placeholder: \"Enter ad title\",\n                        maxLength: 60\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mt-1\",\n                        children: [\n                            formData.ad_title.length,\n                            \"/60 characters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 45,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        htmlFor: \"ad-description\",\n                        children: \"Ad Description\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                        id: \"ad-description\",\n                        value: formData.ad_description,\n                        onChange: (e)=>updateFormData({\n                                ad_description: e.target.value\n                            }),\n                        placeholder: \"Enter ad description\",\n                        rows: 3,\n                        maxLength: 150\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mt-1\",\n                        children: [\n                            formData.ad_description.length,\n                            \"/150 characters\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 56,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        htmlFor: \"image-url\",\n                        children: \"Image URL\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        id: \"image-url\",\n                        value: formData.image_url,\n                        onChange: (e)=>updateFormData({\n                                image_url: e.target.value\n                            }),\n                        placeholder: \"https://example.com/image.jpg\",\n                        type: \"url\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mt-1\",\n                        children: \"Enter a direct URL to your ad image (for now, file upload will be added later)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 68,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        htmlFor: \"destination-url\",\n                        children: \"Destination URL *\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                        id: \"destination-url\",\n                        value: formData.destination_url,\n                        onChange: (e)=>updateFormData({\n                                destination_url: e.target.value\n                            }),\n                        placeholder: \"https://example.com\",\n                        type: \"url\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 81,\n                columnNumber: 4\n            }, this),\n            formData.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        children: \"Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border rounded-lg p-4 max-w-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: formData.image_url,\n                                alt: \"Ad preview\",\n                                className: \"w-full h-32 object-cover rounded mb-2\",\n                                onError: (e)=>{\n                                    e.currentTarget.style.display = \"none\";\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-sm\",\n                                children: formData.ad_title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: formData.ad_description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 92,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, this);\n}\n_c = AdCreativeStep;\nfunction TargetingStep(param) {\n    let { formData, updateFormData } = param;\n    const countries = [\n        \"US\",\n        \"CA\",\n        \"GB\",\n        \"AU\",\n        \"DE\",\n        \"FR\",\n        \"JP\",\n        \"BR\",\n        \"IN\",\n        \"CN\"\n    ];\n    const devices = [\n        \"desktop\",\n        \"mobile\",\n        \"tablet\"\n    ];\n    const languages = [\n        \"en\",\n        \"es\",\n        \"fr\",\n        \"de\",\n        \"pt\",\n        \"ja\",\n        \"zh\",\n        \"hi\",\n        \"ar\",\n        \"ru\"\n    ];\n    const interests = [\n        \"technology\",\n        \"finance\",\n        \"health\",\n        \"education\",\n        \"entertainment\",\n        \"sports\",\n        \"travel\",\n        \"food\",\n        \"fashion\",\n        \"gaming\"\n    ];\n    const ageRanges = [\n        \"18-24\",\n        \"25-34\",\n        \"35-44\",\n        \"45-54\",\n        \"55-64\",\n        \"65+\"\n    ];\n    const toggleArrayItem = (array, item)=>{\n        return array.includes(item) ? array.filter((i)=>i !== item) : [\n            ...array,\n            item\n        ];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm font-medium\",\n                        children: \"Countries\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mb-2\",\n                        children: \"Select target countries (all selected by default)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 md:grid-cols-6 gap-2\",\n                        children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__.Checkbox, {\n                                        id: \"country-\".concat(country),\n                                        checked: formData.targeting.countries.includes(country),\n                                        onCheckedChange: ()=>{\n                                            updateFormData({\n                                                targeting: {\n                                                    ...formData.targeting,\n                                                    countries: toggleArrayItem(formData.targeting.countries, country)\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"country-\".concat(country),\n                                        className: \"text-sm\",\n                                        children: country\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, country, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 7\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 142,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm font-medium\",\n                        children: \"Devices\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mb-2\",\n                        children: \"Select target devices (all selected by default)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__.Checkbox, {\n                                        id: \"device-\".concat(device),\n                                        checked: formData.targeting.devices.includes(device),\n                                        onCheckedChange: ()=>{\n                                            updateFormData({\n                                                targeting: {\n                                                    ...formData.targeting,\n                                                    devices: toggleArrayItem(formData.targeting.devices, device)\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"device-\".concat(device),\n                                        className: \"text-sm capitalize\",\n                                        children: device\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, device, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 7\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 168,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm font-medium\",\n                        children: \"Languages\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-muted-foreground mb-2\",\n                        children: \"Select target languages (all selected by default)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 md:grid-cols-6 gap-2\",\n                        children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__.Checkbox, {\n                                        id: \"language-\".concat(language),\n                                        checked: formData.targeting.languages.includes(language),\n                                        onCheckedChange: ()=>{\n                                            updateFormData({\n                                                targeting: {\n                                                    ...formData.targeting,\n                                                    languages: toggleArrayItem(formData.targeting.languages, language)\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"language-\".concat(language),\n                                        className: \"text-sm uppercase\",\n                                        children: language\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, language, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 7\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 194,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-base font-medium\",\n                        children: \"Interests\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-3\",\n                        children: \"Select target interests (leave empty for all interests)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                        children: interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__.Checkbox, {\n                                        id: \"interest-\".concat(interest),\n                                        checked: formData.targeting.interests.includes(interest),\n                                        onCheckedChange: ()=>{\n                                            updateFormData({\n                                                targeting: {\n                                                    ...formData.targeting,\n                                                    interests: toggleArrayItem(formData.targeting.interests, interest)\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"interest-\".concat(interest),\n                                        className: \"text-sm capitalize\",\n                                        children: interest\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, interest, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 7\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 220,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-base font-medium\",\n                        children: \"Age Ranges\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-3\",\n                        children: \"Select target age ranges (leave empty for all ages)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: ageRanges.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__.Checkbox, {\n                                        id: \"age-\".concat(ageRange),\n                                        checked: formData.targeting.age_ranges.includes(ageRange),\n                                        onCheckedChange: ()=>{\n                                            updateFormData({\n                                                targeting: {\n                                                    ...formData.targeting,\n                                                    age_ranges: toggleArrayItem(formData.targeting.age_ranges, ageRange)\n                                                }\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"age-\".concat(ageRange),\n                                        className: \"text-sm\",\n                                        children: ageRange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, ageRange, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 7\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 248,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n        lineNumber: 141,\n        columnNumber: 3\n    }, this);\n}\n_c1 = TargetingStep;\nfunction ReviewStep(param) {\n    let { formData } = param;\n    var _formData_start_date, _formData_end_date;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium mb-4\",\n                        children: \"Campaign Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Campaign Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: formData.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Budget\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            formData.budget_type === \"total\" && \"$\".concat(formData.total_budget, \" total\"),\n                                            formData.budget_type === \"cpc\" && \"$\".concat(formData.budget_cpc, \" per click\"),\n                                            formData.budget_type === \"cpm\" && \"$\".concat(formData.budget_cpm, \" per 1000 impressions\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Start Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: (_formData_start_date = formData.start_date) === null || _formData_start_date === void 0 ? void 0 : _formData_start_date.toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"End Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: (_formData_end_date = formData.end_date) === null || _formData_end_date === void 0 ? void 0 : _formData_end_date.toLocaleDateString()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Ad Placement\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: formData.slot_id ? \"Slot ID: \".concat(formData.slot_id) : \"Not selected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 282,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium mb-4\",\n                        children: \"Ad Creative\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            formData.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: formData.image_url,\n                                alt: \"Ad preview\",\n                                className: \"w-24 h-24 object-cover rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium\",\n                                        children: formData.ad_title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: formData.ad_description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mt-1\",\n                                        children: formData.destination_url\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 312,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium mb-4\",\n                        children: \"Targeting\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            formData.targeting.countries.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Countries:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: formData.targeting.countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                variant: \"secondary\",\n                                                children: country\n                                            }, country, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 7\n                            }, this),\n                            formData.targeting.devices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Devices:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: formData.targeting.devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                variant: \"secondary\",\n                                                children: device\n                                            }, device, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 7\n                            }, this),\n                            formData.targeting.languages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Languages:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: formData.targeting.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                variant: \"secondary\",\n                                                children: language\n                                            }, language, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 7\n                            }, this),\n                            formData.targeting.interests.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Interests:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: formData.targeting.interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                variant: \"secondary\",\n                                                children: interest\n                                            }, interest, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 7\n                            }, this),\n                            formData.targeting.age_ranges.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Age Ranges:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                        children: formData.targeting.age_ranges.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                variant: \"secondary\",\n                                                children: ageRange\n                                            }, ageRange, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 10\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 7\n                            }, this),\n                            Object.values(formData.targeting).every((arr)=>arr.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"No targeting restrictions - will show to all users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 326,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n        lineNumber: 281,\n        columnNumber: 3\n    }, this);\n}\n_c2 = ReviewStep;\nfunction PlacementStep(param) {\n    let { formData, updateFormData } = param;\n    _s();\n    // Fetcher function for SWR\n    const fetcher = (url)=>fetch(url).then((res)=>res.json());\n    // Fetch available ad slots\n    const { data: slotsData, error, isLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(\"/api/placements\", fetcher);\n    // Local state for filtering and sorting\n    const [pageFilter, setPageFilter] = useState(\"all\");\n    const [sortBy, setSortBy] = useState(\"name\");\n    const [sortOrder, setSortOrder] = useState(\"asc\");\n    const slots = (slotsData === null || slotsData === void 0 ? void 0 : slotsData.data) || [];\n    // Filtered and sorted slots\n    const filteredAndSortedSlots = useMemo({\n        \"PlacementStep.useMemo[filteredAndSortedSlots]\": ()=>{\n            let filtered = slots;\n            // Apply page filter\n            if (pageFilter !== \"all\") {\n                filtered = slots.filter({\n                    \"PlacementStep.useMemo[filteredAndSortedSlots]\": (slot)=>slot.page === pageFilter\n                }[\"PlacementStep.useMemo[filteredAndSortedSlots]\"]);\n            }\n            // Apply sorting\n            filtered.sort({\n                \"PlacementStep.useMemo[filteredAndSortedSlots]\": (a, b)=>{\n                    let aValue, bValue;\n                    switch(sortBy){\n                        case \"price_cpc\":\n                            aValue = a.price_cpc || 0;\n                            bValue = b.price_cpc || 0;\n                            break;\n                        case \"price_cpm\":\n                            aValue = a.price_cpm || 0;\n                            bValue = b.price_cpm || 0;\n                            break;\n                        case \"estimated_views\":\n                            aValue = a.estimated_views || 0;\n                            bValue = b.estimated_views || 0;\n                            break;\n                        case \"name\":\n                        default:\n                            aValue = a.name.toLowerCase();\n                            bValue = b.name.toLowerCase();\n                            break;\n                    }\n                    if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\n                    if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\n                    return 0;\n                }\n            }[\"PlacementStep.useMemo[filteredAndSortedSlots]\"]);\n            return filtered;\n        }\n    }[\"PlacementStep.useMemo[filteredAndSortedSlots]\"], [\n        slots,\n        pageFilter,\n        sortBy,\n        sortOrder\n    ]);\n    // Handle placement selection\n    const handlePlacementToggle = (slotId)=>{\n        const currentSelections = formData.selected_placements;\n        const isSelected = currentSelections.includes(slotId);\n        if (isSelected) {\n            // Remove from selection\n            const newSelections = currentSelections.filter((id)=>id !== slotId);\n            const newCreatives = {\n                ...formData.placement_creatives\n            };\n            delete newCreatives[slotId];\n            updateFormData({\n                selected_placements: newSelections,\n                placement_creatives: newCreatives\n            });\n        } else {\n            // Add to selection\n            const newSelections = [\n                ...currentSelections,\n                slotId\n            ];\n            const newCreatives = {\n                ...formData.placement_creatives,\n                [slotId]: {\n                    ad_title: \"\",\n                    ad_description: \"\",\n                    image_url: \"\",\n                    destination_url: \"\"\n                }\n            };\n            updateFormData({\n                selected_placements: newSelections,\n                placement_creatives: newCreatives\n            });\n        }\n    };\n    // Get unique page types for filter\n    const pageTypes = useMemo({\n        \"PlacementStep.useMemo[pageTypes]\": ()=>{\n            const types = [\n                ...new Set(slots.map({\n                    \"PlacementStep.useMemo[pageTypes]\": (slot)=>slot.page\n                }[\"PlacementStep.useMemo[pageTypes]\"]))\n            ];\n            return types.filter({\n                \"PlacementStep.useMemo[pageTypes]\": (type)=>type !== \"all\"\n            }[\"PlacementStep.useMemo[pageTypes]\"]);\n        }\n    }[\"PlacementStep.useMemo[pageTypes]\"], [\n        slots\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"Loading ad placements...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 505,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n            lineNumber: 504,\n            columnNumber: 4\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-destructive\",\n                children: \"Failed to load ad placements. Please try again.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 516,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n            lineNumber: 515,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-base font-medium\",\n                        children: \"Choose Ad Placements\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 524,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground mb-4\",\n                        children: \"Select one or more placements where you want your ads to appear. You'll create separate ad creatives for each selected placement in the next step.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 523,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4 p-4 bg-muted/50 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"page-filter\",\n                                className: \"text-sm font-medium\",\n                                children: \"Filter by Page Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                                value: pageFilter,\n                                onValueChange: setPageFilter,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectValue, {\n                                            placeholder: \"All pages\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"all\",\n                                                children: \"All Pages\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 8\n                                            }, this),\n                                            pageTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                    value: type,\n                                                    children: type === \"home\" ? \"Home\" : type === \"subnets\" ? \"Subnets\" : type === \"companies\" ? \"Companies\" : type === \"newsletter\" ? \"Newsletter\" : type\n                                                }, type, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 9\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"sort-by\",\n                                className: \"text-sm font-medium\",\n                                children: \"Sort by\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                                value: sortBy,\n                                onValueChange: setSortBy,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"name\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"price_cpc\",\n                                                children: \"CPC Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"price_cpm\",\n                                                children: \"CPM Price\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"estimated_views\",\n                                                children: \"Estimated Views\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"sort-order\",\n                                className: \"text-sm font-medium\",\n                                children: \"Order\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Select, {\n                                value: sortOrder,\n                                onValueChange: (value)=>setSortOrder(value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectTrigger, {\n                                        className: \"mt-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectValue, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"asc\",\n                                                children: \"Ascending\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectItem, {\n                                                value: \"desc\",\n                                                children: \"Descending\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 532,\n                columnNumber: 4\n            }, this),\n            formData.selected_placements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-primary/10 border border-primary/20 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm font-medium text-primary\",\n                    children: [\n                        formData.selected_placements.length,\n                        \" placement\",\n                        formData.selected_placements.length !== 1 ? \"s\" : \"\",\n                        \" selected\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                    lineNumber: 596,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 595,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: filteredAndSortedSlots.map((slot)=>{\n                    var _slot_estimated_views;\n                    const isSelected = formData.selected_placements.includes(slot.id);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border rounded-lg p-4 cursor-pointer transition-colors \".concat(isSelected ? \"border-primary bg-primary/5\" : \"border-border hover:border-primary/50\"),\n                        onClick: ()=>handlePlacementToggle(slot.id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_2__.Checkbox, {\n                                                checked: isSelected,\n                                                onChange: ()=>handlePlacementToggle(slot.id),\n                                                className: \"text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 619,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: slot.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: slot.page === \"all\" ? \"All Pages\" : slot.page === \"home\" ? \"Home\" : slot.page === \"subnets\" ? \"Subnets\" : slot.page === \"companies\" ? \"Companies\" : slot.page === \"newsletter\" ? \"Newsletter\" : slot.page\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 10\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mb-3\",\n                                        children: slot.description || \"Ad slot for \".concat(slot.page, \" page\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 10\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Size:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    slot.width,\n                                                    \" \\xd7 \",\n                                                    slot.height,\n                                                    \"px\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Est. Views:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    ((_slot_estimated_views = slot.estimated_views) === null || _slot_estimated_views === void 0 ? void 0 : _slot_estimated_views.toLocaleString()) || \"N/A\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"CPC:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    \"$\",\n                                                    slot.price_cpc || \"N/A\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"CPM:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 12\n                                                    }, this),\n                                                    \"$\",\n                                                    slot.price_cpm || \"N/A\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 10\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 8\n                        }, this)\n                    }, slot.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 7\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 604,\n                columnNumber: 4\n            }, this),\n            filteredAndSortedSlots.length === 0 && slots.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"No placements match the current filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 671,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        className: \"mt-2\",\n                        onClick: ()=>{\n                            setPageFilter(\"all\");\n                            setSortBy(\"name\");\n                            setSortOrder(\"asc\");\n                        },\n                        children: \"Clear Filters\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 670,\n                columnNumber: 5\n            }, this),\n            slots.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: \"No ad placements available at the moment.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                    lineNumber: 689,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n                lineNumber: 688,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\components\\\\campaigns\\\\campaign-wizard-steps.tsx\",\n        lineNumber: 522,\n        columnNumber: 3\n    }, this);\n}\n_s(PlacementStep, \"JAjmvgXUV8JbGa68oW+4ImjIkrw=\", false, function() {\n    return [\n        swr__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c3 = PlacementStep;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"AdCreativeStep\");\n$RefreshReg$(_c1, \"TargetingStep\");\n$RefreshReg$(_c2, \"ReviewStep\");\n$RefreshReg$(_c3, \"PlacementStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/campaigns/campaign-wizard-steps.tsx\n"));

/***/ })

});