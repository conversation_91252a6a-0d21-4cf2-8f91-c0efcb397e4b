/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/campaigns/route";
exports.ids = ["app/api/user/campaigns/route"];
exports.modules = {

/***/ "(rsc)/./app/api/user/campaigns/route.ts":
/*!*****************************************!*\
  !*** ./app/api/user/campaigns/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// app/api/user/campaigns/route.ts\n\n\nasync function GET() {\n    try {\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error:\", response.status, errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `API Error: ${response.status} - ${errorText}`\n            }, {\n                status: response.status\n            });\n        }\n        const result = await response.json();\n        // Handle standardized response format\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: result.data,\n                message: result.message\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: result.message,\n                errors: result.errors\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"Campaigns fetch error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Internal server error\",\n            error: error.code || \"unknown_error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        const body = await req.json();\n        console.log(\"Received campaign data:\", body);\n        // Get user info and company info\n        const userResponse = await fetch(`${process.env.API_BASE_URL}/user/me`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!userResponse.ok) {\n            throw new Error(\"Failed to get user information\");\n        }\n        const userResult = await userResponse.json();\n        const userId = userResult.success ? userResult.data.id : null;\n        console.log(\"User lookup result:\", userResult);\n        if (!userId) {\n            throw new Error(\"User ID not found\");\n        }\n        // Get user's company info for advertiser_id\n        const companyResponse = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        console.log(\"Company response status:\", companyResponse.status);\n        if (!companyResponse.ok) {\n            const errorText = await companyResponse.text();\n            console.error(\"Company lookup failed:\", companyResponse.status, errorText);\n            throw new Error(`Failed to get company information: ${errorText}`);\n        }\n        const companyResult = await companyResponse.json();\n        console.log(\"Company lookup result:\", companyResult);\n        const companyId = companyResult.success ? companyResult.data.id : null;\n        if (!companyId) {\n            throw new Error(\"Company ID not found - user must have a company to create campaigns\");\n        }\n        console.log(\"Using company ID:\", companyId, \"and user ID:\", userId);\n        // 1. First create the Campaign\n        const campaignResponse = await fetch(`${process.env.API_BASE_URL}/campaigns`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            },\n            body: JSON.stringify({\n                advertiser_id: body.advertiser_id || companyId,\n                manager_id: userId,\n                name: body.name,\n                start_date: body.start_date,\n                end_date: body.end_date,\n                total_budget: body.total_budget || null,\n                budget_cpc: body.budget_cpc || null,\n                budget_cpm: body.budget_cpm || null\n            })\n        });\n        console.log(\"Campaign response status:\", campaignResponse.status);\n        if (!campaignResponse.ok) {\n            const errorText = await campaignResponse.text();\n            console.error(\"Campaign creation failed:\", campaignResponse.status, errorText);\n            throw new Error(`Campaign creation failed: ${errorText}`);\n        }\n        const campaignResult = await campaignResponse.json();\n        console.log(\"Campaign creation result:\", campaignResult);\n        // Handle campaign creation response\n        if (!campaignResult.success) {\n            console.error(\"Campaign creation unsuccessful:\", campaignResult);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: campaignResult.message,\n                errors: campaignResult.errors\n            }, {\n                status: 400\n            });\n        }\n        // 2. Create ads for each selected placement\n        const createdAds = [];\n        const placements = body.placements || [];\n        if (placements.length === 0) {\n            // Fallback for old single placement format\n            const adData = {\n                campaign_id: campaignResult.data.id,\n                slot_id: body.slot_id || 1,\n                title: body.title || body.name,\n                image_url: body.image_url || \"\",\n                target_url: body.target_url || body.destination_url,\n                max_impressions: body.max_impressions || null,\n                max_clicks: body.max_clicks || null,\n                weight: body.weight || 1\n            };\n            const adResponse = await fetch(`${process.env.API_BASE_URL}/ads`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: `Bearer ${token}`\n                },\n                body: JSON.stringify(adData)\n            });\n            if (!adResponse.ok) {\n                const errorText = await adResponse.text();\n                throw new Error(`Ad creation failed: ${errorText}`);\n            }\n            const adResult = await adResponse.json();\n            if (!adResult.success) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: adResult.message,\n                    errors: adResult.errors\n                }, {\n                    status: 400\n                });\n            }\n            createdAds.push(adResult.data);\n        } else {\n            // Create multiple ads for multiple placements\n            for (const placement of placements){\n                const adData = {\n                    campaign_id: campaignResult.data.id,\n                    slot_id: placement.slot_id,\n                    title: placement.title,\n                    image_url: placement.image_url || \"\",\n                    target_url: placement.target_url,\n                    max_impressions: placement.max_impressions || null,\n                    max_clicks: placement.max_clicks || null,\n                    weight: placement.weight || 1\n                };\n                const adResponse = await fetch(`${process.env.API_BASE_URL}/ads`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: `Bearer ${token}`\n                    },\n                    body: JSON.stringify(adData)\n                });\n                if (!adResponse.ok) {\n                    const errorText = await adResponse.text();\n                    console.error(`Ad creation failed for slot ${placement.slot_id}:`, errorText);\n                    continue;\n                }\n                const adResult = await adResponse.json();\n                if (adResult.success && adResult.data) {\n                    createdAds.push(adResult.data);\n                }\n            }\n            if (createdAds.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Failed to create any ads for the campaign\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // 3. Create targeting rules if provided (apply to all ads in the campaign)\n        if (body.targeting && createdAds.length > 0) {\n            for (const ad of createdAds){\n                try {\n                    const targetingResponse = await fetch(`${process.env.API_BASE_URL}/ads/${ad.id}/targeting`, {\n                        method: \"POST\",\n                        headers: {\n                            \"Content-Type\": \"application/json\",\n                            Authorization: `Bearer ${token}`\n                        },\n                        body: JSON.stringify(body.targeting)\n                    });\n                    console.log(`Targeting response status for ad ${ad.id}:`, targetingResponse.status);\n                    if (!targetingResponse.ok) {\n                        const errorText = await targetingResponse.text();\n                        console.warn(`Targeting creation failed for ad ${ad.id} (non-critical):`, targetingResponse.status, errorText);\n                    // Don't fail the entire campaign creation if targeting fails\n                    } else {\n                        const targetingResult = await targetingResponse.json();\n                        console.log(`Targeting creation result for ad ${ad.id}:`, targetingResult);\n                    }\n                } catch (error) {\n                    console.warn(`Targeting creation error for ad ${ad.id} (non-critical):`, error);\n                // Don't fail the entire campaign creation if targeting fails\n                }\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            data: {\n                campaign: campaignResult.data,\n                ads: createdAds\n            },\n            message: `Campaign created successfully with ${createdAds.length} ad${createdAds.length !== 1 ? \"s\" : \"\"}`\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message,\n            details: error.stack\n        }, {\n            status: 400\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/campaigns/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/campaigns/route.ts */ \"(rsc)/./app/api/user/campaigns/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/campaigns/route\",\n        pathname: \"/api/user/campaigns\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/campaigns/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\api\\\\user\\\\campaigns\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();