/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user/campaigns/route";
exports.ids = ["app/api/user/campaigns/route"];
exports.modules = {

/***/ "(rsc)/./app/api/user/campaigns/route.ts":
/*!*****************************************!*\
  !*** ./app/api/user/campaigns/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_auth0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth0 */ \"(rsc)/./lib/auth0.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n// app/api/user/campaigns/route.ts\n\n\nasync function GET() {\n    try {\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        const response = await fetch(`${process.env.API_BASE_URL}/campaigns`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error:\", response.status, errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `API Error: ${response.status} - ${errorText}`\n            }, {\n                status: response.status\n            });\n        }\n        const result = await response.json();\n        // Handle standardized response format\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: result.data,\n                message: result.message\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: result.message,\n                errors: result.errors\n            }, {\n                status: 400\n            });\n        }\n    } catch (error) {\n        console.error(\"Campaigns fetch error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Internal server error\",\n            error: error.code || \"unknown_error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const { token } = await _lib_auth0__WEBPACK_IMPORTED_MODULE_0__.auth0.getAccessToken();\n        const body = await req.json();\n        console.log(\"Received campaign data:\", body);\n        // Get user info and company info\n        const userResponse = await fetch(`${process.env.API_BASE_URL}/user/me`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        if (!userResponse.ok) {\n            throw new Error(\"Failed to get user information\");\n        }\n        const userResult = await userResponse.json();\n        const userId = userResult.success ? userResult.data.id : null;\n        console.log(\"User lookup result:\", userResult);\n        if (!userId) {\n            throw new Error(\"User ID not found\");\n        }\n        // Get user's company info for advertiser_id\n        const companyResponse = await fetch(`${process.env.API_BASE_URL}/user/companies/me`, {\n            headers: {\n                Authorization: `Bearer ${token}`\n            }\n        });\n        console.log(\"Company response status:\", companyResponse.status);\n        if (!companyResponse.ok) {\n            const errorText = await companyResponse.text();\n            console.error(\"Company lookup failed:\", companyResponse.status, errorText);\n            throw new Error(`Failed to get company information: ${errorText}`);\n        }\n        const companyResult = await companyResponse.json();\n        console.log(\"Company lookup result:\", companyResult);\n        const companyId = companyResult.success ? companyResult.data.id : null;\n        if (!companyId) {\n            throw new Error(\"Company ID not found - user must have a company to create campaigns\");\n        }\n        console.log(\"Using company ID:\", companyId, \"and user ID:\", userId);\n        // 1. First create the Campaign\n        const campaignResponse = await fetch(`${process.env.API_BASE_URL}/campaigns`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${token}`\n            },\n            body: JSON.stringify({\n                advertiser_id: body.advertiser_id || companyId,\n                manager_id: userId,\n                name: body.name,\n                start_date: body.start_date,\n                end_date: body.end_date,\n                total_budget: body.total_budget || null,\n                budget_cpc: body.budget_cpc || null,\n                budget_cpm: body.budget_cpm || null\n            })\n        });\n        console.log(\"Campaign response status:\", campaignResponse.status);\n        if (!campaignResponse.ok) {\n            const errorText = await campaignResponse.text();\n            console.error(\"Campaign creation failed:\", campaignResponse.status, errorText);\n            throw new Error(`Campaign creation failed: ${errorText}`);\n        }\n        const campaignResult = await campaignResponse.json();\n        console.log(\"Campaign creation result:\", campaignResult);\n        // Handle campaign creation response\n        if (!campaignResult.success) {\n            console.error(\"Campaign creation unsuccessful:\", campaignResult);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: campaignResult.message,\n                errors: campaignResult.errors\n            }, {\n                status: 400\n            });\n        }\n        // 2. Create ads for each selected placement\n        const createdAds = [];\n        const placements = body.placements || [];\n        if (placements.length === 0) {\n            // Fallback for old single placement format\n            const adData = {\n                campaign_id: campaignResult.data.id,\n                slot_id: body.slot_id || 1,\n                title: body.title || body.name,\n                image_url: body.image_url || \"\",\n                target_url: body.target_url || body.destination_url,\n                max_impressions: body.max_impressions || null,\n                max_clicks: body.max_clicks || null,\n                weight: body.weight || 1\n            };\n            const adResponse = await fetch(`${process.env.API_BASE_URL}/ads`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: `Bearer ${token}`\n                },\n                body: JSON.stringify(adData)\n            });\n            if (!adResponse.ok) {\n                const errorText = await adResponse.text();\n                throw new Error(`Ad creation failed: ${errorText}`);\n            }\n            const adResult = await adResponse.json();\n            if (!adResult.success) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: adResult.message,\n                    errors: adResult.errors\n                }, {\n                    status: 400\n                });\n            }\n            createdAds.push(adResult.data);\n        } else {\n            // Create multiple ads for multiple placements\n            for (const placement of placements){\n                const adData = {\n                    campaign_id: campaignResult.data.id,\n                    slot_id: placement.slot_id,\n                    title: placement.title,\n                    image_url: placement.image_url || \"\",\n                    target_url: placement.target_url,\n                    max_impressions: placement.max_impressions || null,\n                    max_clicks: placement.max_clicks || null,\n                    weight: placement.weight || 1\n                };\n                const adResponse = await fetch(`${process.env.API_BASE_URL}/ads`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: `Bearer ${token}`\n                    },\n                    body: JSON.stringify(adData)\n                });\n                if (!adResponse.ok) {\n                    const errorText = await adResponse.text();\n                    console.error(`Ad creation failed for slot ${placement.slot_id}:`, errorText);\n                    continue;\n                }\n                const adResult = await adResponse.json();\n                if (adResult.success && adResult.data) {\n                    createdAds.push(adResult.data);\n                }\n            }\n            if (createdAds.length === 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Failed to create any ads for the campaign\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // 3. Create targeting rules if provided (campaign-level targeting)\n        if (body.targeting) {\n            try {\n                const targetingResponse = await fetch(`${process.env.API_BASE_URL}/campaigns/${campaignResult.data.id}/targeting`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        Authorization: `Bearer ${token}`\n                    },\n                    body: JSON.stringify(body.targeting)\n                });\n                console.log(`Campaign targeting response status:`, targetingResponse.status);\n                if (!targetingResponse.ok) {\n                    const errorText = await targetingResponse.text();\n                    console.warn(`Campaign targeting creation failed (non-critical):`, targetingResponse.status, errorText);\n                // Don't fail the entire campaign creation if targeting fails\n                } else {\n                    const targetingResult = await targetingResponse.json();\n                    console.log(`Campaign targeting creation result:`, targetingResult);\n                }\n            } catch (error) {\n                console.warn(`Campaign targeting creation error (non-critical):`, error);\n            // Don't fail the entire campaign creation if targeting fails\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            data: {\n                campaign: campaignResult.data,\n                ads: createdAds\n            },\n            message: `Campaign created successfully with ${createdAds.length} ad${createdAds.length !== 1 ? \"s\" : \"\"}`\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message,\n            details: error.stack\n        }, {\n            status: 400\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/user/campaigns/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth0.ts":
/*!**********************!*\
  !*** ./lib/auth0.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth0: () => (/* binding */ auth0)\n/* harmony export */ });\n/* harmony import */ var _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth0/nextjs-auth0/server */ \"(rsc)/./node_modules/@auth0/nextjs-auth0/dist/server/index.js\");\n// lib/auth0.js\n\n// Initialize the Auth0 client\nconst auth0 = new _auth0_nextjs_auth0_server__WEBPACK_IMPORTED_MODULE_0__.Auth0Client({\n    session: {\n        rolling: true,\n        cookie: {\n            name: \"app_session\",\n            path: \"/\",\n            sameSite: \"lax\",\n            secure: \"development\" === \"production\"\n        }\n    },\n    authorizationParameters: {\n        scope: process.env.AUTH0_SCOPE,\n        audience: process.env.AUTH0_AUDIENCE\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvYXV0aDAudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxlQUFlO0FBRTBDO0FBRXpELDhCQUE4QjtBQUN2QixNQUFNQyxRQUFRLElBQUlELG1FQUFXQSxDQUFDO0lBQ3BDRSxTQUFTO1FBQ1JDLFNBQVM7UUFDVEMsUUFBUTtZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRQyxrQkFBeUI7UUFDbEM7SUFDRDtJQUNBQyx5QkFBeUI7UUFDeEJDLE9BQU9GLFFBQVFHLEdBQUcsQ0FBQ0MsV0FBVztRQUM5QkMsVUFBVUwsUUFBUUcsR0FBRyxDQUFDRyxjQUFjO0lBQ3JDO0FBQ0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiZW5qaVxcRGVza3RvcFxcR19QUk9HXFxOaWNvbGFzXFxidGRhc2gtZWNvc3lzdGVtXFxidGRhc2gtYWRtYW5hZ2VyXFxsaWJcXGF1dGgwLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGxpYi9hdXRoMC5qc1xyXG5cclxuaW1wb3J0IHsgQXV0aDBDbGllbnQgfSBmcm9tIFwiQGF1dGgwL25leHRqcy1hdXRoMC9zZXJ2ZXJcIjtcclxuXHJcbi8vIEluaXRpYWxpemUgdGhlIEF1dGgwIGNsaWVudFxyXG5leHBvcnQgY29uc3QgYXV0aDAgPSBuZXcgQXV0aDBDbGllbnQoe1xyXG5cdHNlc3Npb246IHtcclxuXHRcdHJvbGxpbmc6IHRydWUsXHJcblx0XHRjb29raWU6IHtcclxuXHRcdFx0bmFtZTogXCJhcHBfc2Vzc2lvblwiLFxyXG5cdFx0XHRwYXRoOiBcIi9cIixcclxuXHRcdFx0c2FtZVNpdGU6IFwibGF4XCIsXHJcblx0XHRcdHNlY3VyZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwicHJvZHVjdGlvblwiLFxyXG5cdFx0fSxcclxuXHR9LFxyXG5cdGF1dGhvcml6YXRpb25QYXJhbWV0ZXJzOiB7XHJcblx0XHRzY29wZTogcHJvY2Vzcy5lbnYuQVVUSDBfU0NPUEUsXHJcblx0XHRhdWRpZW5jZTogcHJvY2Vzcy5lbnYuQVVUSDBfQVVESUVOQ0UsXHJcblx0fSxcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJBdXRoMENsaWVudCIsImF1dGgwIiwic2Vzc2lvbiIsInJvbGxpbmciLCJjb29raWUiLCJuYW1lIiwicGF0aCIsInNhbWVTaXRlIiwic2VjdXJlIiwicHJvY2VzcyIsImF1dGhvcml6YXRpb25QYXJhbWV0ZXJzIiwic2NvcGUiLCJlbnYiLCJBVVRIMF9TQ09QRSIsImF1ZGllbmNlIiwiQVVUSDBfQVVESUVOQ0UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth0.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/user/campaigns/route.ts */ \"(rsc)/./app/api/user/campaigns/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user/campaigns/route\",\n        pathname: \"/api/user/campaigns\",\n        filename: \"route\",\n        bundlePath: \"app/api/user/campaigns/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\api\\\\user\\\\campaigns\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdash_admanager_app_api_user_campaigns_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/oauth4webapi","vendor-chunks/@auth0","vendor-chunks/@edge-runtime","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser%2Fcampaigns%2Froute&page=%2Fapi%2Fuser%2Fcampaigns%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser%2Fcampaigns%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdash-admanager&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();