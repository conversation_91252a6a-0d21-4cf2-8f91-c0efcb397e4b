"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";
import { AdCreativeStep, PlacementStep, ReviewStep, TargetingStep } from "./campaign-wizard-steps";

interface CampaignWizardProps {
	onComplete: (campaignData: any) => void;
	onCancel: () => void;
}

interface PlacementAdCreative {
	ad_title: string;
	ad_description: string;
	image_url: string;
	destination_url: string;
}

interface CampaignFormData {
	// Basic Info
	name: string;

	// Budget
	total_budget: number | null;
	budget_cpc: number | null;
	budget_cpm: number | null;
	budget_type: "cpc" | "cpm" | "total";

	// Schedule
	start_date: Date | null;
	end_date: Date | null;

	// Multiple placements support
	selected_placements: number[];
	placement_creatives: Record<number, PlacementAdCreative>;

	// Targeting
	targeting: {
		countries: string[];
		devices: string[];
		languages: string[];
		interests: string[];
		age_ranges: string[];
	};
}

const STEPS = [
	{ id: 1, title: "Basic Info", description: "Campaign name" },
	{ id: 2, title: "Budget", description: "Set your budget and pricing" },
	{ id: 3, title: "Schedule", description: "Campaign start and end dates" },
	{ id: 4, title: "Placement", description: "Choose ad placement" },
	{ id: 5, title: "Ad Creative", description: "Create your ad content" },
	{ id: 6, title: "Targeting", description: "Define your audience" },
	{ id: 7, title: "Review", description: "Review and submit" },
];

export function CampaignWizard({ onComplete, onCancel }: CampaignWizardProps) {
	const [currentStep, setCurrentStep] = useState(1);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { toast } = useToast();

	const [formData, setFormData] = useState<CampaignFormData>({
		name: "",
		total_budget: null,
		budget_cpc: null,
		budget_cpm: null,
		budget_type: "total",
		start_date: null,
		end_date: null,
		selected_placements: [],
		placement_creatives: {},
		targeting: {
			countries: ["US", "CA", "GB", "AU", "DE", "FR", "JP", "BR", "IN", "CN"], // All countries selected by default
			devices: ["desktop", "mobile", "tablet"], // All devices selected by default
			languages: ["en", "es", "fr", "de", "pt", "ja", "zh", "hi", "ar", "ru"], // All languages selected by default
			interests: [
				"technology",
				"finance",
				"health",
				"education",
				"entertainment",
				"sports",
				"travel",
				"food",
				"fashion",
				"gaming",
			], // All interests selected by default
			age_ranges: ["18-24", "25-34", "35-44", "45-54", "55-64", "65+"], // All age ranges selected by default
		},
	});

	const updateFormData = (updates: Partial<CampaignFormData>) => {
		setFormData((prev) => ({ ...prev, ...updates }));
	};

	const nextStep = () => {
		if (currentStep < STEPS.length) {
			setCurrentStep(currentStep + 1);
		}
	};

	const prevStep = () => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
		}
	};

	const validateStep = (step: number): boolean => {
		switch (step) {
			case 1:
				return formData.name.trim() !== "";
			case 2:
				return formData.total_budget !== null && formData.total_budget > 0;
			case 3:
				return formData.start_date !== null && formData.end_date !== null;
			case 4:
				return formData.selected_placements.length > 0;
			case 5:
				// Validate that all selected placements have ad creatives
				return formData.selected_placements.every((placementId) => {
					const creative = formData.placement_creatives[placementId];
					return creative && creative.ad_title.trim() !== "" && creative.destination_url.trim() !== "";
				});
			case 6:
				return true; // Targeting is optional
			default:
				return true;
		}
	};

	const handleSubmit = async () => {
		if (!validateStep(currentStep)) {
			toast({
				title: "Validation Error",
				description: "Please fill in all required fields",
				variant: "destructive",
			});
			return;
		}

		setIsSubmitting(true);
		try {
			await onComplete(formData);
		} catch (error) {
			toast({
				title: "Error",
				description: "Failed to create campaign. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const renderStepContent = () => {
		switch (currentStep) {
			case 1:
				return <BasicInfoStep formData={formData} updateFormData={updateFormData} />;
			case 2:
				return <BudgetStep formData={formData} updateFormData={updateFormData} />;
			case 3:
				return <ScheduleStep formData={formData} updateFormData={updateFormData} />;
			case 4:
				return <PlacementStep formData={formData} updateFormData={updateFormData} />;
			case 5:
				return <AdCreativeStep formData={formData} updateFormData={updateFormData} />;
			case 6:
				return <TargetingStep formData={formData} updateFormData={updateFormData} />;
			case 7:
				return <ReviewStep formData={formData} />;
			default:
				return null;
		}
	};

	return (
		<div className="max-w-5xl mx-auto p-4">
			{/* Progress Steps - Compact */}
			<div className="mb-6">
				<div className="flex items-center justify-between">
					{STEPS.map((step, index) => (
						<div key={step.id} className="flex items-center">
							<div
								className={cn(
									"flex items-center justify-center w-7 h-7 rounded-full text-xs font-medium",
									currentStep >= step.id
										? "bg-primary text-primary-foreground"
										: "bg-muted text-muted-foreground"
								)}
							>
								{step.id}
							</div>
							<div className="ml-2 hidden md:block">
								<p className="text-xs font-medium">{step.title}</p>
							</div>
							{index < STEPS.length - 1 && <div className="w-6 h-px bg-muted mx-3 hidden sm:block" />}
						</div>
					))}
				</div>
			</div>

			{/* Step Content - Larger */}
			<Card className="min-h-[500px]">
				<CardHeader className="pb-4">
					<CardTitle className="text-lg">{STEPS[currentStep - 1].title}</CardTitle>
					<CardDescription className="text-sm">{STEPS[currentStep - 1].description}</CardDescription>
				</CardHeader>
				<CardContent className="pt-0">
					<div className="min-h-[400px]">{renderStepContent()}</div>
				</CardContent>
			</Card>

			{/* Navigation - Compact */}
			<div className="flex justify-between mt-4">
				<div>
					{currentStep > 1 && (
						<Button variant="outline" onClick={prevStep} size="sm">
							<ChevronLeft className="w-4 h-4 mr-1" />
							Previous
						</Button>
					)}
				</div>
				<div className="space-x-2">
					<Button variant="outline" onClick={onCancel} size="sm">
						Cancel
					</Button>
					{currentStep < STEPS.length ? (
						<Button onClick={nextStep} disabled={!validateStep(currentStep)} size="sm">
							Next
							<ChevronRight className="w-4 h-4 ml-1" />
						</Button>
					) : (
						<Button onClick={handleSubmit} disabled={!validateStep(currentStep) || isSubmitting} size="sm">
							{isSubmitting ? "Creating..." : "Create Campaign"}
						</Button>
					)}
				</div>
			</div>
		</div>
	);
}

// Step Components
function BasicInfoStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	return (
		<div className="space-y-4">
			<div>
				<Label htmlFor="name">Campaign Name *</Label>
				<Input
					id="name"
					value={formData.name}
					onChange={(e) => updateFormData({ name: e.target.value })}
					placeholder="Enter campaign name"
				/>
			</div>
		</div>
	);
}

function BudgetStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	return (
		<div className="space-y-4">
			<div>
				<Label htmlFor="total-budget">Total Budget ($) *</Label>
				<Input
					id="total-budget"
					type="number"
					min="1"
					step="0.01"
					value={formData.total_budget || ""}
					onChange={(e) => updateFormData({ total_budget: parseFloat(e.target.value) || null })}
					placeholder="Enter total budget"
				/>
				<p className="text-xs text-muted-foreground mt-1">
					Set the total amount you want to spend on this campaign
				</p>
			</div>
		</div>
	);
}

function ScheduleStep({
	formData,
	updateFormData,
}: {
	formData: CampaignFormData;
	updateFormData: (updates: Partial<CampaignFormData>) => void;
}) {
	return (
		<div className="space-y-4">
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label>Start Date *</Label>
					<Popover>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									!formData.start_date && "text-muted-foreground"
								)}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{formData.start_date ? format(formData.start_date, "PPP") : "Pick a date"}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0">
							<Calendar
								mode="single"
								selected={formData.start_date || undefined}
								onSelect={(date) => updateFormData({ start_date: date || null })}
								disabled={(date) => date < new Date()}
								initialFocus
							/>
						</PopoverContent>
					</Popover>
				</div>
				<div>
					<Label>End Date *</Label>
					<Popover>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									!formData.end_date && "text-muted-foreground"
								)}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{formData.end_date ? format(formData.end_date, "PPP") : "Pick a date"}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0">
							<Calendar
								mode="single"
								selected={formData.end_date || undefined}
								onSelect={(date) => updateFormData({ end_date: date || null })}
								disabled={(date) => date < (formData.start_date || new Date())}
								initialFocus
							/>
						</PopoverContent>
					</Popover>
				</div>
			</div>
		</div>
	);
}
