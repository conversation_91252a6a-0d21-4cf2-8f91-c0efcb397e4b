"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/requests/[id]/page",{

/***/ "(app-pages-browser)/./app/admin/requests/[id]/page.tsx":
/*!******************************************!*\
  !*** ./app/admin/requests/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdRequestReviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdRequestReviewPage(param) {\n    let { params } = param;\n    var _campaign_targeting, _campaign_targeting_countries_include, _campaign_targeting_countries_exclude, _campaign_targeting1, _campaign_targeting_pageTypes_types, _campaign_targeting2, _campaign_targeting3, _campaign_targeting4, _campaign_targeting5;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_2__.use)(params);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [placements, setPlacements] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [advertiser, setAdvertiser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [manager, setManager] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [selectedReason, setSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [rejectionReasons, setRejectionReasons] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overlappingCampaigns, setOverlappingCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AdRequestReviewPage.useEffect\": ()=>{\n            const fetchCampaignDetails = {\n                \"AdRequestReviewPage.useEffect.fetchCampaignDetails\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch campaign details from API\n                        const response = await fetch(\"/api/user/campaigns/\".concat(id));\n                        const result = await response.json();\n                        if (!result.success || !result.data) {\n                            toast({\n                                title: \"Campaign not found\",\n                                description: \"The requested campaign could not be found.\",\n                                variant: \"destructive\"\n                            });\n                            router.push(\"/admin/requests\");\n                            return;\n                        }\n                        const campaignData = result.data;\n                        setCampaign(campaignData);\n                        // For now, we'll skip overlapping campaigns check since it requires complex logic\n                        // This can be implemented later with a dedicated admin endpoint\n                        setOverlappingCampaigns([]);\n                        // Fetch ads for this campaign\n                        try {\n                            const adsResponse = await fetch(\"/api/user/campaigns/\".concat(id, \"/ads\"));\n                            const adsResult = await adsResponse.json();\n                            if (adsResult.success && adsResult.data) {\n                                setAds(adsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch ads data:\", error);\n                        }\n                        // Fetch all placements to map slot IDs to placement details\n                        try {\n                            const placementsResponse = await fetch(\"/api/placements\");\n                            const placementsResult = await placementsResponse.json();\n                            if (placementsResult.success && placementsResult.data) {\n                                setPlacements(placementsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch placements data:\", error);\n                        }\n                        // Set advertiser info from campaign data (this is the company)\n                        if (campaignData.advertiser_name) {\n                            setAdvertiser({\n                                id: campaignData.advertiser_id,\n                                name: campaignData.advertiser_name,\n                                email: campaignData.advertiser_email || \"\",\n                                role: \"company\"\n                            });\n                        }\n                        // Fetch rejection reasons\n                        try {\n                            const reasonsResponse = await fetch(\"/api/admin/rejection-reasons?entity_type=campaign\");\n                            const reasonsResult = await reasonsResponse.json();\n                            if (reasonsResult.success) {\n                                setRejectionReasons(reasonsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch rejection reasons:\", error);\n                        }\n                        // Set manager info from campaign data (this is the user managing the campaign)\n                        if (campaignData.manager_name) {\n                            setManager({\n                                id: campaignData.manager_id,\n                                name: campaignData.manager_name,\n                                email: campaignData.manager_email || \"\",\n                                role: \"user\"\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching campaign details:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load campaign details. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdRequestReviewPage.useEffect.fetchCampaignDetails\"];\n            fetchCampaignDetails();\n        }\n    }[\"AdRequestReviewPage.useEffect\"], [\n        id,\n        router,\n        toast\n    ]);\n    const handleApprove = async ()=>{\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/approve\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: feedback || \"Campaign approved\"\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to approve campaign\");\n            }\n            toast({\n                title: \"Campaign approved\",\n                description: \"The ad campaign has been approved and is now active.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error approving campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to approve the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleReject = async ()=>{\n        if (!selectedReason) {\n            toast({\n                title: \"Rejection reason required\",\n                description: \"Please select a rejection reason before rejecting the campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/reject\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    reason: selectedReason,\n                    notes: feedback.trim() || null\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to reject campaign\");\n            }\n            toast({\n                title: \"Campaign rejected\",\n                description: \"The ad campaign has been rejected with feedback.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error rejecting campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to reject the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex h-[70vh] items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg\",\n                        children: \"Loading campaign details...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 224,\n            columnNumber: 4\n        }, this);\n    }\n    if (!campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"The requested campaign could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 7\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 235,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mb-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 6\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: campaign.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            children: [\n                                                ads.length,\n                                                \" placement\",\n                                                ads.length !== 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: campaign.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 250,\n                columnNumber: 4\n            }, this),\n            overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                variant: \"destructive\",\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertTitle, {\n                        children: \"Scheduling Conflict Detected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: [\n                            \"This campaign overlaps with \",\n                            overlappingCampaigns.length,\n                            \" existing approved campaign\",\n                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                            \" on the same placement.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                            defaultValue: \"overview\",\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"overview\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"creative\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"targeting\",\n                                            children: \"Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 8\n                                        }, this),\n                                        overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"conflicts\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Conflicts\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-white\",\n                                                    children: overlappingCampaigns.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"overview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Campaign Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign details and settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-4 md:grid-cols-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Campaign Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Name:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 311,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 312,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"URL:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 317,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.url\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Duration:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 323,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: [\n                                                                                        formatDate(campaign.startDate),\n                                                                                        \" -\",\n                                                                                        \" \",\n                                                                                        formatDate(campaign.endDate)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 324,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Submitted:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 330,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: formatDate(campaign.createdAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: [\n                                                                        \"Placement Information (\",\n                                                                        ads.length,\n                                                                        \" placements)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                ads.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"No placement information available\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-3\",\n                                                                    children: ads.map((ad, index)=>{\n                                                                        const placement1 = placements.find((p)=>p.id === ad.slot_id);\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2 rounded-lg border p-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2 mb-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                            className: \"font-medium text-sm\",\n                                                                                            children: [\n                                                                                                \"Placement #\",\n                                                                                                index + 1\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 359,\n                                                                                            columnNumber: 18\n                                                                                        }, this),\n                                                                                        placement1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    variant: \"secondary\",\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: placement1.page === \"all\" ? \"All Pages\" : placement1.page === \"home\" ? \"Home\" : placement1.page === \"subnets\" ? \"Subnets\" : placement1.page === \"companies\" ? \"Companies\" : placement1.page\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 364,\n                                                                                                    columnNumber: 20\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    variant: \"outline\",\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: [\n                                                                                                        placement1.width,\n                                                                                                        \" \\xd7 \",\n                                                                                                        placement1.height,\n                                                                                                        \"px\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 378,\n                                                                                                    columnNumber: 20\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 358,\n                                                                                    columnNumber: 17\n                                                                                }, this),\n                                                                                placement1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: \"Name:\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 391,\n                                                                                                    columnNumber: 20\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                                    children: placement1.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 394,\n                                                                                                    columnNumber: 20\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 390,\n                                                                                            columnNumber: 19\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: \"Page:\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 399,\n                                                                                                    columnNumber: 20\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                                    children: placement1.page\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 402,\n                                                                                                    columnNumber: 20\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 398,\n                                                                                            columnNumber: 19\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: \"Dimensions:\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 407,\n                                                                                                    columnNumber: 20\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                                    children: [\n                                                                                                        placement1.width,\n                                                                                                        \" \\xd7 \",\n                                                                                                        placement1.height,\n                                                                                                        \"px\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 410,\n                                                                                                    columnNumber: 20\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 406,\n                                                                                            columnNumber: 19\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: \"CPC Price:\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 415,\n                                                                                                    columnNumber: 20\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                                    children: [\n                                                                                                        \"$\",\n                                                                                                        placement1.price_cpc || \"N/A\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 418,\n                                                                                                    columnNumber: 20\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 414,\n                                                                                            columnNumber: 19\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: \"CPM Price:\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 423,\n                                                                                                    columnNumber: 20\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                                    children: [\n                                                                                                        \"$\",\n                                                                                                        placement1.price_cpm || \"N/A\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 426,\n                                                                                                    columnNumber: 20\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 422,\n                                                                                            columnNumber: 19\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: [\n                                                                                        \"Placement details not available for slot\",\n                                                                                        \" \",\n                                                                                        ad.slot_id\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 432,\n                                                                                    columnNumber: 18\n                                                                                }, this)\n                                                                            ]\n                                                                        }, ad.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 16\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"creative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: [\n                                                            \"Ad Creatives (\",\n                                                            ads.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the advertisement creatives and content for each placement\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: ads.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"No ads found for this campaign.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 11\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: ads.map((ad, index)=>{\n                                                        const placement1 = placements.find((p)=>p.id === ad.slot_id);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mb-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: [\n                                                                                            \"Ad #\",\n                                                                                            index + 1,\n                                                                                            \": \",\n                                                                                            ad.title\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 471,\n                                                                                        columnNumber: 18\n                                                                                    }, this),\n                                                                                    placement1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                className: \"text-xs\",\n                                                                                                children: placement1.page === \"all\" ? \"All Pages\" : placement1.page === \"home\" ? \"Home\" : placement1.page === \"subnets\" ? \"Subnets\" : placement1.page === \"companies\" ? \"Companies\" : placement1.page\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 476,\n                                                                                                columnNumber: 20\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                variant: \"outline\",\n                                                                                                className: \"text-xs\",\n                                                                                                children: [\n                                                                                                    placement1.width,\n                                                                                                    \" \\xd7 \",\n                                                                                                    placement1.height,\n                                                                                                    \"px\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 490,\n                                                                                                columnNumber: 20\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 17\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                className: \"text-xs font-medium\",\n                                                                                                children: \"Title:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 502,\n                                                                                                columnNumber: 19\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm\",\n                                                                                                children: ad.title\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 505,\n                                                                                                columnNumber: 19\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 501,\n                                                                                        columnNumber: 18\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                className: \"text-xs font-medium\",\n                                                                                                children: \"Destination URL:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 508,\n                                                                                                columnNumber: 19\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                children: ad.target_url\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 511,\n                                                                                                columnNumber: 19\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 507,\n                                                                                        columnNumber: 18\n                                                                                    }, this),\n                                                                                    placement1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                                                                className: \"text-xs font-medium\",\n                                                                                                children: \"Placement:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 517,\n                                                                                                columnNumber: 20\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm\",\n                                                                                                children: placement1.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 520,\n                                                                                                columnNumber: 20\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                children: placement1.description\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 521,\n                                                                                                columnNumber: 20\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 516,\n                                                                                        columnNumber: 19\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 500,\n                                                                                columnNumber: 17\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 16\n                                                                    }, this),\n                                                                    ad.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-32 h-24 overflow-hidden rounded border bg-muted\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: ad.image_url,\n                                                                                alt: ad.title,\n                                                                                className: \"w-full h-full object-contain\",\n                                                                                onError: (e)=>{\n                                                                                    e.currentTarget.style.display = \"none\";\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 18\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        }, ad.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 14\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 11\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"targeting\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Targeting Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign targeting configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Geographic Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting = campaign.targeting) === null || _campaign_targeting === void 0 ? void 0 : _campaign_targeting.countries) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Mode: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"capitalize\",\n                                                                                    children: campaign.targeting.countries.mode\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 567,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Included Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 574,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_include = campaign.targeting.countries.include) === null || _campaign_targeting_countries_include === void 0 ? void 0 : _campaign_targeting_countries_include.length) > 0 ? campaign.targeting.countries.include.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 581,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 587,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Excluded Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 597,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_exclude = campaign.targeting.countries.exclude) === null || _campaign_targeting_countries_exclude === void 0 ? void 0 : _campaign_targeting_countries_exclude.length) > 0 ? campaign.targeting.countries.exclude.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 604,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 610,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Targeting all countries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No geographic targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Page Type Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting1 = campaign.targeting) === null || _campaign_targeting1 === void 0 ? void 0 : _campaign_targeting1.pageTypes) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Selected Page Types:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 638,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_pageTypes_types = campaign.targeting.pageTypes.types) === null || _campaign_targeting_pageTypes_types === void 0 ? void 0 : _campaign_targeting_pageTypes_types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: type\n                                                                                        }, type, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 643,\n                                                                                            columnNumber: 17\n                                                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No page types specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 647,\n                                                                                        columnNumber: 17\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 641,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 637,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.pageTypes.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Category Targeting:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 space-y-2\",\n                                                                                    children: Object.entries(campaign.targeting.pageTypes.categories).map((param)=>{\n                                                                                        let [pageType, categories] = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"rounded border p-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm font-medium capitalize\",\n                                                                                                    children: [\n                                                                                                        pageType,\n                                                                                                        \":\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 19\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                                    children: categories === \"all\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                        variant: \"outline\",\n                                                                                                        children: \"All Categories\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 669,\n                                                                                                        columnNumber: 21\n                                                                                                    }, this) : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            children: category\n                                                                                                        }, category, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                            lineNumber: 675,\n                                                                                                            columnNumber: 23\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 667,\n                                                                                                    columnNumber: 19\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, pageType, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 663,\n                                                                                            columnNumber: 18\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 659,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No page type targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Device Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting2 = campaign.targeting) === null || _campaign_targeting2 === void 0 ? void 0 : _campaign_targeting2.devices) && campaign.targeting.devices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: device\n                                                                        }, device, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 705,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No device targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Language Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting3 = campaign.targeting) === null || _campaign_targeting3 === void 0 ? void 0 : _campaign_targeting3.languages) && campaign.targeting.languages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: language.toUpperCase()\n                                                                        }, language, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 723,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No language targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Interest Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting4 = campaign.targeting) === null || _campaign_targeting4 === void 0 ? void 0 : _campaign_targeting4.interests) && campaign.targeting.interests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"capitalize\",\n                                                                            children: interest\n                                                                        }, interest, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 743,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No interest targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Age Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting5 = campaign.targeting) === null || _campaign_targeting5 === void 0 ? void 0 : _campaign_targeting5.age) && campaign.targeting.age.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.age.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: ageRange\n                                                                        }, ageRange, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 768,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No age targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"conflicts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Scheduling Conflicts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: [\n                                                            \"This campaign overlaps with \",\n                                                            overlappingCampaigns.length,\n                                                            \" existing approved campaign\",\n                                                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                            \" on the same placement\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        overlappingCampaigns.map((overlapCampaign)=>{\n                                                            var _placement;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-2 flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium\",\n                                                                                children: overlapCampaign.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 800,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"capitalize\",\n                                                                                children: overlapCampaign.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 801,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 799,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-3 flex items-center gap-2 text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 806,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    formatDate(overlapCampaign.startDate),\n                                                                                    \" -\",\n                                                                                    \" \",\n                                                                                    formatDate(overlapCampaign.endDate)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 807,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 805,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Placement\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 814,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 815,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Advertiser\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 818,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: (advertiser === null || advertiser === void 0 ? void 0 : advertiser.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 819,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 817,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, overlapCampaign.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 12\n                                                            }, this);\n                                                        }),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Conflict Resolution Options\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 826,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"ml-5 list-disc space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Adjust the campaign dates to avoid overlap\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Reject this campaign with feedback about the conflict\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Approve anyway (multiple ads will rotate in the same placement)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Cancel one of the existing campaigns to make room\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 825,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Advertiser Information (Company)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: advertiser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary\",\n                                                            children: advertiser.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: advertiser.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 857,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: advertiser.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 856,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"Company Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: advertiser.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: advertiser.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 848,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Advertiser information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Campaign Manager (User)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: manager ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-600\",\n                                                            children: manager.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 887,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: manager.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: manager.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"User Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 899,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: manager.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 902,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Role:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: manager.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 885,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Manager information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 879,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Campaign Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Review the campaign timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Campaign Duration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Start Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 930,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.startDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 931,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"End Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 934,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.endDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 935,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Duration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(campaign.endDate).getTime() - new Date(campaign.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        \"days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 937,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 9\n                                                }, this),\n                                                overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-red-600 dark:text-red-400\",\n                                                                    children: \"Scheduling Conflict\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 dark:text-red-400\",\n                                                            children: [\n                                                                \"This campaign overlaps with \",\n                                                                overlappingCampaigns.length,\n                                                                \" existing approved campaign\",\n                                                                overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                                \" on the same placement.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Review Decision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 971,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Approve or reject this ad campaign request\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 972,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 970,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"rejection-reason\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Rejection Reason\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                            value: selectedReason,\n                                                            onValueChange: setSelectedReason,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                        placeholder: \"Select a rejection reason...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 981,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                    children: rejectionReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: reason.code,\n                                                                            children: reason.description\n                                                                        }, reason.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 13\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 980,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Required for rejection.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 992,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"feedback\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Additional Notes (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_10__.Textarea, {\n                                                            id: \"feedback\",\n                                                            placeholder: \"Provide additional feedback to the advertiser...\",\n                                                            value: feedback,\n                                                            onChange: (e)=>setFeedback(e.target.value),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Optional additional notes for the advertiser.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1005,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleReject,\n                                                disabled: submitting || !selectedReason,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Rejecting...\" : \"Reject\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1012,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"default\",\n                                                onClick: handleApprove,\n                                                disabled: submitting,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Approving...\" : \"Approve\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1020,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1011,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 841,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n        lineNumber: 249,\n        columnNumber: 3\n    }, this);\n}\n_s(AdRequestReviewPage, \"X3Wx5K5Ya7Lmjbiv094qHvsF32A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = AdRequestReviewPage;\nvar _c;\n$RefreshReg$(_c, \"AdRequestReviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/requests/[id]/page.tsx\n"));

/***/ })

});