{"/api/user/campaigns/route": "app/api/user/campaigns/route.js", "/api/user/campaigns/[id]/route": "app/api/user/campaigns/[id]/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/placements/route": "app/api/placements/route.js", "/api/admin/rejection-reasons/route": "app/api/admin/rejection-reasons/route.js", "/api/user/campaigns/[id]/ads/route": "app/api/user/campaigns/[id]/ads/route.js", "/dashboard/placements/page": "app/dashboard/placements/page.js", "/dashboard/campaigns/page": "app/dashboard/campaigns/page.js", "/admin/page": "app/admin/page.js", "/admin/requests/[id]/page": "app/admin/requests/[id]/page.js", "/admin/requests/page": "app/admin/requests/page.js"}