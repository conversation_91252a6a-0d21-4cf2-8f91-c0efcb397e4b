"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/requests/[id]/page",{

/***/ "(app-pages-browser)/./app/admin/requests/[id]/page.tsx":
/*!******************************************!*\
  !*** ./app/admin/requests/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdRequestReviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction AdRequestReviewPage(param) {\n    let { params } = param;\n    var _placement, _placement1, _placement2, _placement3, _campaign_targeting, _campaign_targeting_countries_include, _campaign_targeting_countries_exclude, _campaign_targeting1, _campaign_targeting_pageTypes_types, _campaign_targeting2, _campaign_targeting3, _campaign_targeting4, _campaign_targeting5;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_2__.use)(params);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [placements, setPlacements] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [advertiser, setAdvertiser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [manager, setManager] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [selectedReason, setSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [rejectionReasons, setRejectionReasons] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overlappingCampaigns, setOverlappingCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AdRequestReviewPage.useEffect\": ()=>{\n            const fetchCampaignDetails = {\n                \"AdRequestReviewPage.useEffect.fetchCampaignDetails\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch campaign details from API\n                        const response = await fetch(\"/api/user/campaigns/\".concat(id));\n                        const result = await response.json();\n                        if (!result.success || !result.data) {\n                            toast({\n                                title: \"Campaign not found\",\n                                description: \"The requested campaign could not be found.\",\n                                variant: \"destructive\"\n                            });\n                            router.push(\"/admin/requests\");\n                            return;\n                        }\n                        const campaignData = result.data;\n                        setCampaign(campaignData);\n                        // For now, we'll skip overlapping campaigns check since it requires complex logic\n                        // This can be implemented later with a dedicated admin endpoint\n                        setOverlappingCampaigns([]);\n                        // Fetch placement data if available\n                        if (campaignData.slot_id) {\n                            try {\n                                const placementResponse = await fetch(\"/api/placements/\".concat(campaignData.slot_id));\n                                const placementResult = await placementResponse.json();\n                                if (placementResult.success) {\n                                    setPlacement(placementResult.data);\n                                }\n                            } catch (error) {\n                                console.log(\"Could not fetch placement data:\", error);\n                            }\n                        }\n                        // Set advertiser info from campaign data (this is the company)\n                        if (campaignData.advertiser_name) {\n                            setAdvertiser({\n                                id: campaignData.advertiser_id,\n                                name: campaignData.advertiser_name,\n                                email: campaignData.advertiser_email || \"\",\n                                role: \"company\"\n                            });\n                        }\n                        // Fetch rejection reasons\n                        try {\n                            const reasonsResponse = await fetch(\"/api/admin/rejection-reasons?entity_type=campaign\");\n                            const reasonsResult = await reasonsResponse.json();\n                            if (reasonsResult.success) {\n                                setRejectionReasons(reasonsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch rejection reasons:\", error);\n                        }\n                        // Set manager info from campaign data (this is the user managing the campaign)\n                        if (campaignData.manager_name) {\n                            setManager({\n                                id: campaignData.manager_id,\n                                name: campaignData.manager_name,\n                                email: campaignData.manager_email || \"\",\n                                role: \"user\"\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching campaign details:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load campaign details. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdRequestReviewPage.useEffect.fetchCampaignDetails\"];\n            fetchCampaignDetails();\n        }\n    }[\"AdRequestReviewPage.useEffect\"], [\n        id,\n        router,\n        toast\n    ]);\n    const handleApprove = async ()=>{\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/approve\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: feedback || \"Campaign approved\"\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to approve campaign\");\n            }\n            toast({\n                title: \"Campaign approved\",\n                description: \"The ad campaign has been approved and is now active.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error approving campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to approve the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleReject = async ()=>{\n        if (!selectedReason) {\n            toast({\n                title: \"Rejection reason required\",\n                description: \"Please select a rejection reason before rejecting the campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/reject\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    reason: selectedReason,\n                    notes: feedback.trim() || null\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to reject campaign\");\n            }\n            toast({\n                title: \"Campaign rejected\",\n                description: \"The ad campaign has been rejected with feedback.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error rejecting campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to reject the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex h-[70vh] items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg\",\n                        children: \"Loading campaign details...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 4\n        }, this);\n    }\n    if (!campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"The requested campaign could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 7\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mb-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 6\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: campaign.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.type) || \"Unknown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: campaign.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 4\n            }, this),\n            overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                variant: \"destructive\",\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertTitle, {\n                        children: \"Scheduling Conflict Detected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: [\n                            \"This campaign overlaps with \",\n                            overlappingCampaigns.length,\n                            \" existing approved campaign\",\n                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                            \" on the same placement.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                            defaultValue: \"overview\",\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"overview\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"creative\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"targeting\",\n                                            children: \"Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 8\n                                        }, this),\n                                        overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"conflicts\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Conflicts\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-white\",\n                                                    children: overlappingCampaigns.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"overview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Campaign Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign details and settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-4 md:grid-cols-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Campaign Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Name:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"URL:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 305,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.url\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 306,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 304,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Duration:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 311,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: [\n                                                                                        formatDate(campaign.startDate),\n                                                                                        \" -\",\n                                                                                        \" \",\n                                                                                        formatDate(campaign.endDate)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 312,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Submitted:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: formatDate(campaign.createdAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Placement Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: placement ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Name:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 332,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 333,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Type:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 338,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 339,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Dimensions:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 344,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.dimensions\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Price:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 352,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.priceDisplay\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 355,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Placement information not available\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"creative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Ad Creative\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the advertisement creative and content\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"overflow-hidden rounded-lg border\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"aspect-video w-full overflow-hidden bg-muted\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: campaign.imageUrl || \"/placeholder.svg\",\n                                                                        alt: campaign.name,\n                                                                        className: \"h-full w-full object-contain\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium\",\n                                                                            children: campaign.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: campaign.url\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 390,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Creative Specifications\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Format:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 398,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: ((_placement1 = placement) === null || _placement1 === void 0 ? void 0 : _placement1.format) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Dimensions:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 404,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: ((_placement2 = placement) === null || _placement2 === void 0 ? void 0 : _placement2.dimensions) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 405,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Max File Size:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 410,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: ((_placement3 = placement) === null || _placement3 === void 0 ? void 0 : _placement3.maxFileSize) || \"Unknown\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 413,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"targeting\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Targeting Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign targeting configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Geographic Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting = campaign.targeting) === null || _campaign_targeting === void 0 ? void 0 : _campaign_targeting.countries) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Mode: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 437,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"capitalize\",\n                                                                                    children: campaign.targeting.countries.mode\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 438,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Included Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_include = campaign.targeting.countries.include) === null || _campaign_targeting_countries_include === void 0 ? void 0 : _campaign_targeting_countries_include.length) > 0 ? campaign.targeting.countries.include.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 452,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 458,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 448,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Excluded Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 468,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_exclude = campaign.targeting.countries.exclude) === null || _campaign_targeting_countries_exclude === void 0 ? void 0 : _campaign_targeting_countries_exclude.length) > 0 ? campaign.targeting.countries.exclude.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 475,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 481,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Targeting all countries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No geographic targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Page Type Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting1 = campaign.targeting) === null || _campaign_targeting1 === void 0 ? void 0 : _campaign_targeting1.pageTypes) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Selected Page Types:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_pageTypes_types = campaign.targeting.pageTypes.types) === null || _campaign_targeting_pageTypes_types === void 0 ? void 0 : _campaign_targeting_pageTypes_types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: type\n                                                                                        }, type, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 17\n                                                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No page types specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 17\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.pageTypes.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Category Targeting:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 space-y-2\",\n                                                                                    children: Object.entries(campaign.targeting.pageTypes.categories).map((param)=>{\n                                                                                        let [pageType, categories] = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"rounded border p-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm font-medium capitalize\",\n                                                                                                    children: [\n                                                                                                        pageType,\n                                                                                                        \":\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 535,\n                                                                                                    columnNumber: 19\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                                    children: categories === \"all\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                        variant: \"outline\",\n                                                                                                        children: \"All Categories\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 540,\n                                                                                                        columnNumber: 21\n                                                                                                    }, this) : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            children: category\n                                                                                                        }, category, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                            lineNumber: 546,\n                                                                                                            columnNumber: 23\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 538,\n                                                                                                    columnNumber: 19\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, pageType, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 534,\n                                                                                            columnNumber: 18\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No page type targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Device Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting2 = campaign.targeting) === null || _campaign_targeting2 === void 0 ? void 0 : _campaign_targeting2.devices) && campaign.targeting.devices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: device\n                                                                        }, device, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No device targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Language Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting3 = campaign.targeting) === null || _campaign_targeting3 === void 0 ? void 0 : _campaign_targeting3.languages) && campaign.targeting.languages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: language.toUpperCase()\n                                                                        }, language, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No language targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Interest Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting4 = campaign.targeting) === null || _campaign_targeting4 === void 0 ? void 0 : _campaign_targeting4.interests) && campaign.targeting.interests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"capitalize\",\n                                                                            children: interest\n                                                                        }, interest, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No interest targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Age Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting5 = campaign.targeting) === null || _campaign_targeting5 === void 0 ? void 0 : _campaign_targeting5.age) && campaign.targeting.age.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.age.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: ageRange\n                                                                        }, ageRange, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 639,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No age targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"conflicts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Scheduling Conflicts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: [\n                                                            \"This campaign overlaps with \",\n                                                            overlappingCampaigns.length,\n                                                            \" existing approved campaign\",\n                                                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                            \" on the same placement\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        overlappingCampaigns.map((overlapCampaign)=>{\n                                                            var _placement;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-2 flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium\",\n                                                                                children: overlapCampaign.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 671,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"capitalize\",\n                                                                                children: overlapCampaign.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 672,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-3 flex items-center gap-2 text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    formatDate(overlapCampaign.startDate),\n                                                                                    \" -\",\n                                                                                    \" \",\n                                                                                    formatDate(overlapCampaign.endDate)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Placement\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 685,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 686,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 684,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Advertiser\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 689,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: (advertiser === null || advertiser === void 0 ? void 0 : advertiser.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 690,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, overlapCampaign.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 12\n                                                            }, this);\n                                                        }),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Conflict Resolution Options\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"ml-5 list-disc space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Adjust the campaign dates to avoid overlap\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Reject this campaign with feedback about the conflict\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Approve anyway (multiple ads will rotate in the same placement)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Cancel one of the existing campaigns to make room\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Advertiser Information (Company)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: advertiser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary\",\n                                                            children: advertiser.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: advertiser.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: advertiser.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 729,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"Company Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: advertiser.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 736,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: advertiser.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Advertiser information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Campaign Manager (User)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: manager ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-600\",\n                                                            children: manager.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: manager.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: manager.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 766,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"User Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: manager.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Role:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: manager.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Manager information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 750,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Campaign Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Review the campaign timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Campaign Duration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Start Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.startDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"End Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.endDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 806,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Duration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(campaign.endDate).getTime() - new Date(campaign.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        \"days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 9\n                                                }, this),\n                                                overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-red-600 dark:text-red-400\",\n                                                                    children: \"Scheduling Conflict\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 dark:text-red-400\",\n                                                            children: [\n                                                                \"This campaign overlaps with \",\n                                                                overlappingCampaigns.length,\n                                                                \" existing approved campaign\",\n                                                                overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                                \" on the same placement.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 788,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Review Decision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Approve or reject this ad campaign request\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"rejection-reason\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Rejection Reason\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 848,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: selectedReason,\n                                                            onValueChange: setSelectedReason,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select a rejection reason...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: rejectionReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: reason.code,\n                                                                            children: reason.description\n                                                                        }, reason.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 857,\n                                                                            columnNumber: 13\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Required for rejection.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 847,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"feedback\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Additional Notes (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                            id: \"feedback\",\n                                                            placeholder: \"Provide additional feedback to the advertiser...\",\n                                                            value: feedback,\n                                                            onChange: (e)=>setFeedback(e.target.value),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Optional additional notes for the advertiser.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleReject,\n                                                disabled: submitting || !selectedReason,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Rejecting...\" : \"Reject\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"default\",\n                                                onClick: handleApprove,\n                                                disabled: submitting,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Approving...\" : \"Approve\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 712,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n        lineNumber: 239,\n        columnNumber: 3\n    }, this);\n}\n_s(AdRequestReviewPage, \"X3Wx5K5Ya7Lmjbiv094qHvsF32A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = AdRequestReviewPage;\nvar _c;\n$RefreshReg$(_c, \"AdRequestReviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/requests/[id]/page.tsx\n"));

/***/ })

});