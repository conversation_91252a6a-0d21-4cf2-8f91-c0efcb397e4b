"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/campaigns/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/campaigns/create/page.tsx":
/*!*************************************************!*\
  !*** ./app/dashboard/campaigns/create/page.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreateCampaignPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_campaigns_campaign_wizard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/campaigns/campaign-wizard */ \"(app-pages-browser)/./components/campaigns/campaign-wizard.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CreateCampaignPage() {\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const handleCampaignComplete = async (campaignData)=>{\n        try {\n            var _campaignData_start_date, _campaignData_end_date;\n            // Transform the wizard data to match the API format for multiple placements\n            const apiData = {\n                name: campaignData.name,\n                description: campaignData.description,\n                total_budget: campaignData.budget_type === \"total\" ? campaignData.total_budget : null,\n                budget_cpc: campaignData.budget_type === \"cpc\" ? campaignData.budget_cpc : null,\n                budget_cpm: campaignData.budget_type === \"cpm\" ? campaignData.budget_cpm : null,\n                start_date: (_campaignData_start_date = campaignData.start_date) === null || _campaignData_start_date === void 0 ? void 0 : _campaignData_start_date.toISOString().split(\"T\")[0],\n                end_date: (_campaignData_end_date = campaignData.end_date) === null || _campaignData_end_date === void 0 ? void 0 : _campaignData_end_date.toISOString().split(\"T\")[0],\n                // Targeting data\n                targeting: campaignData.targeting,\n                // Multiple placements and their creatives\n                placements: campaignData.selected_placements.map((placementId)=>{\n                    const creative = campaignData.placement_creatives[placementId];\n                    return {\n                        slot_id: placementId,\n                        title: creative.ad_title,\n                        image_url: creative.image_url,\n                        target_url: creative.destination_url,\n                        description: creative.ad_description,\n                        max_impressions: null,\n                        max_clicks: null,\n                        weight: 1\n                    };\n                })\n            };\n            console.log(\"Sending campaign data:\", apiData);\n            const response = await fetch(\"/api/user/campaigns\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(apiData)\n            });\n            console.log(\"Response status:\", response.status);\n            console.log(\"Response headers:\", response.headers);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"HTTP Error:\", response.status, errorText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Response:\", result);\n            if (result.success) {\n                toast({\n                    title: \"Campaign Created\",\n                    description: \"Your campaign has been submitted for review.\"\n                });\n                router.push(\"/dashboard/campaigns\");\n            } else {\n                console.error(\"API Error:\", result);\n                throw new Error(result.message || result.error || \"Failed to create campaign\");\n            }\n        } catch (error) {\n            console.error(\"Campaign creation error:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to create campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n            throw error; // Re-throw to let the wizard handle the loading state\n        }\n    };\n    const handleCancel = ()=>{\n        router.push(\"/dashboard/campaigns\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/dashboard/campaigns\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 7\n                                }, this),\n                                \"Back to Campaigns\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Create New Campaign\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Create a new advertising campaign with our step-by-step wizard.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_campaigns_campaign_wizard__WEBPACK_IMPORTED_MODULE_1__.CampaignWizard, {\n                onComplete: handleCampaignComplete,\n                onCancel: handleCancel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\dashboard\\\\campaigns\\\\create\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 3\n    }, this);\n}\n_s(CreateCampaignPage, \"Rs49VT8nbSWRwJwNEkgn/Lp8XDA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = CreateCampaignPage;\nvar _c;\n$RefreshReg$(_c, \"CreateCampaignPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/campaigns/create/page.tsx\n"));

/***/ })

});