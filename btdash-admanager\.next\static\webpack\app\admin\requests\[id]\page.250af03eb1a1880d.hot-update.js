"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/requests/[id]/page",{

/***/ "(app-pages-browser)/./app/admin/requests/[id]/page.tsx":
/*!******************************************!*\
  !*** ./app/admin/requests/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdRequestReviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,Calendar,Check,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction AdRequestReviewPage(param) {\n    let { params } = param;\n    var _placement, _campaign_targeting, _campaign_targeting_countries_include, _campaign_targeting_countries_exclude, _campaign_targeting1, _campaign_targeting_pageTypes_types, _campaign_targeting2, _campaign_targeting3, _campaign_targeting4, _campaign_targeting5;\n    _s();\n    const { id } = (0,react__WEBPACK_IMPORTED_MODULE_2__.use)(params);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [campaign, setCampaign] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [placements, setPlacements] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [advertiser, setAdvertiser] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [manager, setManager] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [selectedReason, setSelectedReason] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [rejectionReasons, setRejectionReasons] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overlappingCampaigns, setOverlappingCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"AdRequestReviewPage.useEffect\": ()=>{\n            const fetchCampaignDetails = {\n                \"AdRequestReviewPage.useEffect.fetchCampaignDetails\": async ()=>{\n                    try {\n                        setLoading(true);\n                        // Fetch campaign details from API\n                        const response = await fetch(\"/api/user/campaigns/\".concat(id));\n                        const result = await response.json();\n                        if (!result.success || !result.data) {\n                            toast({\n                                title: \"Campaign not found\",\n                                description: \"The requested campaign could not be found.\",\n                                variant: \"destructive\"\n                            });\n                            router.push(\"/admin/requests\");\n                            return;\n                        }\n                        const campaignData = result.data;\n                        setCampaign(campaignData);\n                        // For now, we'll skip overlapping campaigns check since it requires complex logic\n                        // This can be implemented later with a dedicated admin endpoint\n                        setOverlappingCampaigns([]);\n                        // Fetch ads for this campaign\n                        try {\n                            const adsResponse = await fetch(\"/api/user/campaigns/\".concat(id, \"/ads\"));\n                            const adsResult = await adsResponse.json();\n                            if (adsResult.success && adsResult.data) {\n                                setAds(adsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch ads data:\", error);\n                        }\n                        // Fetch all placements to map slot IDs to placement details\n                        try {\n                            const placementsResponse = await fetch(\"/api/placements\");\n                            const placementsResult = await placementsResponse.json();\n                            if (placementsResult.success && placementsResult.data) {\n                                setPlacements(placementsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch placements data:\", error);\n                        }\n                        // Set advertiser info from campaign data (this is the company)\n                        if (campaignData.advertiser_name) {\n                            setAdvertiser({\n                                id: campaignData.advertiser_id,\n                                name: campaignData.advertiser_name,\n                                email: campaignData.advertiser_email || \"\",\n                                role: \"company\"\n                            });\n                        }\n                        // Fetch rejection reasons\n                        try {\n                            const reasonsResponse = await fetch(\"/api/admin/rejection-reasons?entity_type=campaign\");\n                            const reasonsResult = await reasonsResponse.json();\n                            if (reasonsResult.success) {\n                                setRejectionReasons(reasonsResult.data);\n                            }\n                        } catch (error) {\n                            console.log(\"Could not fetch rejection reasons:\", error);\n                        }\n                        // Set manager info from campaign data (this is the user managing the campaign)\n                        if (campaignData.manager_name) {\n                            setManager({\n                                id: campaignData.manager_id,\n                                name: campaignData.manager_name,\n                                email: campaignData.manager_email || \"\",\n                                role: \"user\"\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching campaign details:\", error);\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load campaign details. Please try again.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AdRequestReviewPage.useEffect.fetchCampaignDetails\"];\n            fetchCampaignDetails();\n        }\n    }[\"AdRequestReviewPage.useEffect\"], [\n        id,\n        router,\n        toast\n    ]);\n    const handleApprove = async ()=>{\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/approve\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: feedback || \"Campaign approved\"\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to approve campaign\");\n            }\n            toast({\n                title: \"Campaign approved\",\n                description: \"The ad campaign has been approved and is now active.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error approving campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to approve the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const handleReject = async ()=>{\n        if (!selectedReason) {\n            toast({\n                title: \"Rejection reason required\",\n                description: \"Please select a rejection reason before rejecting the campaign.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSubmitting(true);\n            const response = await fetch(\"/api/admin/campaigns/\".concat(id, \"/reject\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    reason: selectedReason,\n                    notes: feedback.trim() || null\n                })\n            });\n            const result = await response.json();\n            if (!result.success) {\n                throw new Error(result.message || \"Failed to reject campaign\");\n            }\n            toast({\n                title: \"Campaign rejected\",\n                description: \"The ad campaign has been rejected with feedback.\"\n            });\n            router.push(\"/admin/requests\");\n        } catch (error) {\n            console.error(\"Error rejecting campaign:\", error);\n            toast({\n                title: \"Error\",\n                description: error.message || \"Failed to reject the campaign. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    // Format date for display\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto flex h-[70vh] items-center justify-center p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-12 w-12 animate-spin text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg\",\n                        children: \"Loading campaign details...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 4\n        }, this);\n    }\n    if (!campaign) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Campaign not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"The requested campaign could not be found.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 7\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 4\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        className: \"mb-4\",\n                        onClick: ()=>router.push(\"/admin/requests\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 6\n                            }, this),\n                            \"Back to Requests\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold\",\n                                    children: campaign.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-1 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.type) || \"Unknown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"capitalize\",\n                                            children: campaign.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 4\n            }, this),\n            overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                variant: \"destructive\",\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertTitle, {\n                        children: \"Scheduling Conflict Detected\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                        children: [\n                            \"This campaign overlaps with \",\n                            overlappingCampaigns.length,\n                            \" existing approved campaign\",\n                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                            \" on the same placement.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.Tabs, {\n                            defaultValue: \"overview\",\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsList, {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"overview\",\n                                            children: \"Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"creative\",\n                                            children: \"Creative\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"targeting\",\n                                            children: \"Targeting\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 8\n                                        }, this),\n                                        overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsTrigger, {\n                                            value: \"conflicts\",\n                                            className: \"relative\",\n                                            children: [\n                                                \"Conflicts\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-white\",\n                                                    children: overlappingCampaigns.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"overview\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Campaign Overview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign details and settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid gap-4 md:grid-cols-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Campaign Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Name:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 309,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 307,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"URL:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: campaign.url\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 315,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Duration:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 320,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: [\n                                                                                        formatDate(campaign.startDate),\n                                                                                        \" -\",\n                                                                                        \" \",\n                                                                                        formatDate(campaign.endDate)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-3 gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Submitted:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 14\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"col-span-2 text-sm font-medium\",\n                                                                                    children: formatDate(campaign.createdAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 328,\n                                                                                    columnNumber: 14\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Placement Information\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 rounded-lg border p-3\",\n                                                                    children: placement ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Name:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 341,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 342,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Type:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.type\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 348,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 346,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Dimensions:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 353,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.dimensions\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 15\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"grid grid-cols-3 gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-muted-foreground\",\n                                                                                        children: \"Price:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 16\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"col-span-2 text-sm font-medium\",\n                                                                                        children: placement.priceDisplay\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 364,\n                                                                                        columnNumber: 16\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 360,\n                                                                                columnNumber: 15\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Placement information not available\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 14\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"creative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: [\n                                                            \"Ad Creatives (\",\n                                                            ads.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the advertisement creatives and content for each placement\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: ads.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"No ads found for this campaign.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 12\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 11\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: ads.map((ad, index)=>{\n                                                        const placement1 = placements.find((p)=>p.id === ad.slot_id);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mb-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: [\n                                                                                            \"Ad #\",\n                                                                                            index + 1,\n                                                                                            \": \",\n                                                                                            ad.title\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 404,\n                                                                                        columnNumber: 18\n                                                                                    }, this),\n                                                                                    placement1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                className: \"text-xs\",\n                                                                                                children: placement1.page === \"all\" ? \"All Pages\" : placement1.page === \"home\" ? \"Home\" : placement1.page === \"subnets\" ? \"Subnets\" : placement1.page === \"companies\" ? \"Companies\" : placement1.page\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 409,\n                                                                                                columnNumber: 20\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                variant: \"outline\",\n                                                                                                className: \"text-xs\",\n                                                                                                children: [\n                                                                                                    placement1.width,\n                                                                                                    \" \\xd7 \",\n                                                                                                    placement1.height,\n                                                                                                    \"px\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 423,\n                                                                                                columnNumber: 20\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 17\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                                                                className: \"text-xs font-medium\",\n                                                                                                children: \"Title:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 435,\n                                                                                                columnNumber: 19\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm\",\n                                                                                                children: ad.title\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 438,\n                                                                                                columnNumber: 19\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 434,\n                                                                                        columnNumber: 18\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                                                                className: \"text-xs font-medium\",\n                                                                                                children: \"Destination URL:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 441,\n                                                                                                columnNumber: 19\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                children: ad.target_url\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 444,\n                                                                                                columnNumber: 19\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 440,\n                                                                                        columnNumber: 18\n                                                                                    }, this),\n                                                                                    placement1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                                                                className: \"text-xs font-medium\",\n                                                                                                children: \"Placement:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 450,\n                                                                                                columnNumber: 20\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm\",\n                                                                                                children: placement1.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 453,\n                                                                                                columnNumber: 20\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                                children: placement1.description\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 454,\n                                                                                                columnNumber: 20\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 449,\n                                                                                        columnNumber: 19\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 17\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 16\n                                                                    }, this),\n                                                                    ad.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-32 h-24 overflow-hidden rounded border bg-muted\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: ad.image_url,\n                                                                                alt: ad.title,\n                                                                                className: \"w-full h-full object-contain\",\n                                                                                onError: (e)=>{\n                                                                                    e.currentTarget.style.display = \"none\";\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 465,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 18\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        }, ad.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 14\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 11\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"targeting\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Targeting Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: \"Review the campaign targeting configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Geographic Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting = campaign.targeting) === null || _campaign_targeting === void 0 ? void 0 : _campaign_targeting.countries) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Mode: \"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"capitalize\",\n                                                                                    children: campaign.targeting.countries.mode\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"include\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Included Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_include = campaign.targeting.countries.include) === null || _campaign_targeting_countries_include === void 0 ? void 0 : _campaign_targeting_countries_include.length) > 0 ? campaign.targeting.countries.include.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 520,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 510,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"exclude\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Excluded Countries:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_countries_exclude = campaign.targeting.countries.exclude) === null || _campaign_targeting_countries_exclude === void 0 ? void 0 : _campaign_targeting_countries_exclude.length) > 0 ? campaign.targeting.countries.exclude.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: country\n                                                                                        }, country, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 537,\n                                                                                            columnNumber: 20\n                                                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No countries specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 543,\n                                                                                        columnNumber: 18\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 15\n                                                                        }, this),\n                                                                        campaign.targeting.countries.mode === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Targeting all countries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No geographic targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Page Type Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting1 = campaign.targeting) === null || _campaign_targeting1 === void 0 ? void 0 : _campaign_targeting1.pageTypes) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Selected Page Types:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 571,\n                                                                                    columnNumber: 15\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                    children: ((_campaign_targeting_pageTypes_types = campaign.targeting.pageTypes.types) === null || _campaign_targeting_pageTypes_types === void 0 ? void 0 : _campaign_targeting_pageTypes_types.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                            variant: \"secondary\",\n                                                                                            children: type\n                                                                                        }, type, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 576,\n                                                                                            columnNumber: 17\n                                                                                        }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm italic text-muted-foreground\",\n                                                                                        children: \"No page types specified\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 580,\n                                                                                        columnNumber: 17\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 574,\n                                                                                    columnNumber: 15\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 570,\n                                                                            columnNumber: 14\n                                                                        }, this),\n                                                                        campaign.targeting.pageTypes.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: \"Category Targeting:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 589,\n                                                                                    columnNumber: 16\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 space-y-2\",\n                                                                                    children: Object.entries(campaign.targeting.pageTypes.categories).map((param)=>{\n                                                                                        let [pageType, categories] = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"rounded border p-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-sm font-medium capitalize\",\n                                                                                                    children: [\n                                                                                                        pageType,\n                                                                                                        \":\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 597,\n                                                                                                    columnNumber: 19\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"mt-1 flex flex-wrap gap-1\",\n                                                                                                    children: categories === \"all\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                        variant: \"outline\",\n                                                                                                        children: \"All Categories\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 602,\n                                                                                                        columnNumber: 21\n                                                                                                    }, this) : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                            variant: \"secondary\",\n                                                                                                            children: category\n                                                                                                        }, category, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                            lineNumber: 608,\n                                                                                                            columnNumber: 23\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                                    lineNumber: 600,\n                                                                                                    columnNumber: 19\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, pageType, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 596,\n                                                                                            columnNumber: 18\n                                                                                        }, this);\n                                                                                    })\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 592,\n                                                                                    columnNumber: 16\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 15\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No page type targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Device Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting2 = campaign.targeting) === null || _campaign_targeting2 === void 0 ? void 0 : _campaign_targeting2.devices) && campaign.targeting.devices.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: device\n                                                                        }, device, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No device targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 644,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Language Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting3 = campaign.targeting) === null || _campaign_targeting3 === void 0 ? void 0 : _campaign_targeting3.languages) && campaign.targeting.languages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: language.toUpperCase()\n                                                                        }, language, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No language targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Interest Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting4 = campaign.targeting) === null || _campaign_targeting4 === void 0 ? void 0 : _campaign_targeting4.interests) && campaign.targeting.interests.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.interests.map((interest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"capitalize\",\n                                                                            children: interest\n                                                                        }, interest, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No interest targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Age Targeting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 11\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-3\",\n                                                                children: ((_campaign_targeting5 = campaign.targeting) === null || _campaign_targeting5 === void 0 ? void 0 : _campaign_targeting5.age) && campaign.targeting.age.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: campaign.targeting.age.map((ageRange)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            children: ageRange\n                                                                        }, ageRange, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 15\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 13\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"No age targeting configured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 707,\n                                                                    columnNumber: 13\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 11\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_8__.TabsContent, {\n                                    value: \"conflicts\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                        children: \"Scheduling Conflicts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 10\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                        children: [\n                                                            \"This campaign overlaps with \",\n                                                            overlappingCampaigns.length,\n                                                            \" existing approved campaign\",\n                                                            overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                            \" on the same placement\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 10\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 9\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        overlappingCampaigns.map((overlapCampaign)=>{\n                                                            var _placement;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg border p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-2 flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium\",\n                                                                                children: overlapCampaign.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 733,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                variant: \"outline\",\n                                                                                className: \"capitalize\",\n                                                                                children: overlapCampaign.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 734,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mb-3 flex items-center gap-2 text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 739,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: [\n                                                                                    formatDate(overlapCampaign.startDate),\n                                                                                    \" -\",\n                                                                                    \" \",\n                                                                                    formatDate(overlapCampaign.endDate)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 740,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 13\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Placement\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 747,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: ((_placement = placement) === null || _placement === void 0 ? void 0 : _placement.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 748,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 14\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs font-medium\",\n                                                                                        children: \"Advertiser\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 751,\n                                                                                        columnNumber: 15\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: (advertiser === null || advertiser === void 0 ? void 0 : advertiser.name) || \"Unknown\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 752,\n                                                                                        columnNumber: 15\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 750,\n                                                                                columnNumber: 14\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 13\n                                                                    }, this)\n                                                                ]\n                                                            }, overlapCampaign.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 12\n                                                            }, this);\n                                                        }),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900 dark:bg-yellow-950\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Conflict Resolution Options\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"ml-5 list-disc space-y-1 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Adjust the campaign dates to avoid overlap\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Reject this campaign with feedback about the conflict\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 762,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Approve anyway (multiple ads will rotate in the same placement)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 13\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Cancel one of the existing campaigns to make room\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 764,\n                                                                            columnNumber: 13\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 10\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 9\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 718,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Advertiser Information (Company)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: advertiser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary\",\n                                                            children: advertiser.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 783,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: advertiser.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 790,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: advertiser.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"Company Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: advertiser.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Type:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: advertiser.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 794,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Advertiser information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"Campaign Manager (User)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: manager ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-600\",\n                                                            children: manager.name.split(\" \").map((n)=>n[0]).join(\"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: manager.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: manager.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 text-sm font-medium\",\n                                                            children: \"User Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"ID:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: manager.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Role:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm capitalize\",\n                                                                    children: manager.role\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 9\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Manager information not available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 816,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Campaign Schedule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Review the campaign timeline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border p-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Campaign Duration\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Start Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 863,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.startDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 864,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"End Date:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: formatDate(campaign.endDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Duration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"col-span-2 text-sm\",\n                                                                    children: [\n                                                                        Math.ceil((new Date(campaign.endDate).getTime() - new Date(campaign.startDate).getTime()) / (1000 * 60 * 60 * 24)),\n                                                                        \" \",\n                                                                        \"days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 870,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 9\n                                                }, this),\n                                                overlappingCampaigns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-2 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 12\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-red-600 dark:text-red-400\",\n                                                                    children: \"Scheduling Conflict\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 12\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 dark:text-red-400\",\n                                                            children: [\n                                                                \"This campaign overlaps with \",\n                                                                overlappingCampaigns.length,\n                                                                \" existing approved campaign\",\n                                                                overlappingCampaigns.length > 1 ? \"s\" : \"\",\n                                                                \" on the same placement.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Review Decision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Approve or reject this ad campaign request\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"rejection-reason\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Rejection Reason\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: selectedReason,\n                                                            onValueChange: setSelectedReason,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"Select a rejection reason...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 915,\n                                                                        columnNumber: 12\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 914,\n                                                                    columnNumber: 11\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: rejectionReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: reason.code,\n                                                                            children: reason.description\n                                                                        }, reason.code, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 919,\n                                                                            columnNumber: 13\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 917,\n                                                                    columnNumber: 11\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 913,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Required for rejection.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"feedback\",\n                                                            className: \"mb-2 block text-sm font-medium\",\n                                                            children: \"Additional Notes (Optional)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                            id: \"feedback\",\n                                                            placeholder: \"Provide additional feedback to the advertiser...\",\n                                                            value: feedback,\n                                                            onChange: (e)=>setFeedback(e.target.value),\n                                                            className: \"min-h-[100px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs text-muted-foreground\",\n                                                            children: \"Optional additional notes for the advertiser.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 927,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 908,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleReject,\n                                                disabled: submitting || !selectedReason,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Rejecting...\" : \"Reject\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"default\",\n                                                onClick: handleApprove,\n                                                disabled: submitting,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_Calendar_Check_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 9\n                                                    }, this),\n                                                    submitting ? \"Approving...\" : \"Approve\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 953,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                        lineNumber: 774,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdash-admanager\\\\app\\\\admin\\\\requests\\\\[id]\\\\page.tsx\",\n        lineNumber: 248,\n        columnNumber: 3\n    }, this);\n}\n_s(AdRequestReviewPage, \"X3Wx5K5Ya7Lmjbiv094qHvsF32A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = AdRequestReviewPage;\nvar _c;\n$RefreshReg$(_c, \"AdRequestReviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/requests/[id]/page.tsx\n"));

/***/ })

});